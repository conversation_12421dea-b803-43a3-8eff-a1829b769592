<template>
  <div ref="tableWrap" class="im-table" :style="style || {}">
    <slot
      v-if="tableStates.showToolbar"
      name="toolbar"
      v-bind="toolbarSlotScope"
    >
      <div class="im-table-toolbar">
        <div class="left">
          <slot name="toolbar-left" v-bind="toolbarSlotScope"></slot>
        </div>
        <div class="right">
          <slot name="toolbar-right" v-bind="toolbarSlotScope"></slot>
          <el-tooltip
            v-if="tableStates.reloadBar"
            content="刷新"
            placement="top"
            :open-delay="1000"
          >
            <el-button
              :icon="reloadComponent || Refresh"
              circle
              :size="size"
              @click="reload"
            />
          </el-tooltip>

          <el-tooltip
            v-if="tableStates.settingBar"
            content="表格设置"
            placement="top"
            :open-delay="1000"
          >
            <el-button
              :icon="settingComponent || Setting"
              circle
              :size="size"
              @click="showSetting"
            />
          </el-tooltip>
        </div>
      </div>
    </slot>
    <el-alert
      v-if="tableStates.showAlert"
      class="im-table-alert"
      v-bind="tableAlertProps as any"
      @close="emits('on-close-alert')"
    >
      <div class="alert-content">
        <span>当前表格已选择</span>
        <span style="font-weight: bold; margin: 0 4px">{{
          checkedRows.length
        }}</span>
        <span>条</span>
        <slot name="tip"></slot>
      </div>
    </el-alert>
    <el-table
      ref="table"
      class="im-inner-table"
      v-loading="tableLoading"
      border
      v-bind="elTableProps"
      :data="elTableData"
      v-on="{
        ...buildEvents(emits),
        'selection-change': handleSelectionChange
      }"
    >
      <el-table-column
        v-if="showCheckbox"
        type="selection"
        align="center"
      ></el-table-column>
      <im-table-column
        v-if="showIndex"
        :column="{
          label: '序号',
          width: '80px',
          align: 'center',
          render: IndexRender
        }"
      ></im-table-column>

      <slot name="prepend" />
      <im-table-column
        v-for="column in elTableColumns"
        :column="column"
        :key="column.id"
        :class-name="getColumnClassName(column)"
      ></im-table-column>
      <template #append>
        <slot name="append"></slot>
      </template>
      <template #empty>
        <slot name="empty"></slot>
      </template>
      <!-- 操作列-->
      <template v-if="tableStates.showOperator">
        <im-table-column :column="operatorColumn">
          <slot name="operator"></slot>
        </im-table-column>
      </template>
      <slot />
    </el-table>

    <!-- 介于分页和表格之间的内容 -->
    <slot name="footer"></slot>

    <!-- 分页区域 -->
    <div
      v-if="tableStates.showPage && !hidePaginationIfEmpty"
      class="im-table-pagination"
      :style="tableStates.paginationStyle"
    >
      <el-pagination
        v-bind="{ ...defaultPagination(), ...paginationProps }"
        v-model:current-page="paginationProps.currentPage"
        v-model:page-size="paginationProps.pageSize"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      ></el-pagination>
    </div>

    <el-drawer
      v-model="state.drawerVisible"
      size="50%"
      title="设置"
      :before-close="handleDrawerClose"
    >
      <el-form inline>
        <el-form-item label="分页位置">
          <el-select
            placeholder="请选择"
            v-model="paginationProps.align"
            style="width: 120px"
          >
            <el-option label="居左" value="left"></el-option>
            <el-option label="居中" value="center"></el-option>
            <el-option label="居右" value="right"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="默认列位置">
          <el-select
            placeholder="请选择"
            v-model="state.defaultAlign"
            style="width: 120px"
            clearable
          >
            <el-option label="居左" value="left"></el-option>
            <el-option label="居中" value="center"></el-option>
            <el-option label="居右" value="right"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="columnStorage" style="float: right">
          <el-button
            v-if="isRemoteStorage"
            type="primary"
            link
            :icon="Refresh"
            @click="loadRemoteTableColumns(false)"
            >刷新</el-button
          >
          <el-button
            type="success"
            link
            :icon="Checked"
            @click="handleStorageCustomTableSet"
            >保存</el-button
          >
          <el-button
            type="info"
            link
            :icon="Checked"
            @click="loadRemoteTableColumns(true)"
            >默认</el-button
          >
        </el-form-item>
      </el-form>
      <el-alert
        v-if="columnSortable"
        class="im-table-alert"
        type="success"
        :closable="false"
      >
        <div>当前支持通过拖拽改变列位置</div>
      </el-alert>

      <el-divider border-style="dashed"></el-divider>
      <ImTableColumnManage
        :data="state.tableColumns"
        :sortable="!!columnSortable || !!columnStorage"
      ></ImTableColumnManage>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import {
  computed,
  CSSProperties,
  nextTick,
  onBeforeMount,
  onMounted,
  provide,
  reactive,
  ref,
  toRefs,
  useSlots,
  watch
} from "vue";
import {
  ColumnsState,
  CustomTableSet,
  HeaderFilterValue,
  ImTableColumnProps,
  ImTableInstance,
  ImTablePaginationProps,
  IndexRender,
  TableColumnStorage,
  TableContext,
  TableInternalState,
  TableToolbar
} from "./types";
import { ImToolbarSlot } from "./types";
import { Refresh, Setting, Checked } from "@element-plus/icons-vue";
import {
  imTableProps,
  RequestParams,
  RequestResult,
  TableAlert
} from "./props";
import { buildEvents, filterColumnsByProp, genId, debounce } from "./util";
import { createSortable, SortableHandler, SortableOption } from "../sortable";
import { ElMessage, TableInstance } from "element-plus";
import ImTableColumnManage from "./ImTableColumnManage.vue";
import { stopAndPreventDefault } from "@/components/ItsmCommon/util";
import { globalSet } from "@/components/ItsmCommon";
import ImTableColumn from "@/components/ItsmCommon/table/ImTableColumn.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
// 表格绑定dom
const tableWrap = ref<HTMLDivElement>();
// 支持事件列表
const emits = defineEmits([
  "on-data-sort-change",
  "on-columns-storage",
  "on-column-sort-change",
  "on-sort-finished",
  "on-load-before",
  "on-load-success",
  "on-header-dragend",
  "on-page-load",
  "on-pagesize-change",
  "on-page-size-change",
  "on-page-current-change",
  "on-page-change",
  "on-reload",
  "on-setting",
  "on-filter",
  "on-close-alert",
  "on-refresh",
  // 以下是ElTable支持事件列表
  "select",
  "select-all",
  "selection-change",
  "cell-mouse-enter",
  "cell-mouse-leave",
  "cell-contextmenu",
  "cell-click",
  "cell-dblclick",
  "row-click",
  "row-contextmenu",
  "row-dblclick",
  "header-click",
  "header-contextmenu",
  "sort-change",
  "filter-change",
  "current-change",
  "header-dragend",
  "expand-change",
  "scroll"
]);
const slots = useSlots();
const props = defineProps({
  ...imTableProps
});
const table = ref<TableInstance>();
const defaultPagination = (): ImTablePaginationProps => {
  return {
    hideOnEmptyData: false,
    background: true,
    currentPage: 1,
    pageSize: 10,
    layout: "total, sizes, prev, pager, next, jumper"
  };
};
const dragColumnClass = "__im-dragable-column__";
const disableDragColumnClass = "__im-dragable-disabled-column__";
const state = reactive({
  defaultAlign: props.center ? "center" : null,
  tableColumns: [],
  localFilterData: [],
  emitsFilterFlag: false,
  tableData: [],
  pagination: defaultPagination(),
  drawerVisible: false,
  requestLoading: false,

  headerWrapEl: null,
  columnSortableHandler: <SortableHandler>null,
  dataSortableHandler: <SortableHandler>null,

  // 个性化数据（表格不使用，但在保存时作为参数回传给外部注入的方法）
  customInfo: null,
  // 过滤器列表（远程）
  headerFilters: <HeaderFilterValue[]>[]
});

const getHeaderFilterValues = (): HeaderFilterValue[] => {
  return state.headerFilters;
};
const addHeaderFilterValue = (headerFilter: HeaderFilterValue) => {
  let headerFilters = state.headerFilters as HeaderFilterValue[];
  if (!headerFilters.includes(headerFilter)) {
    headerFilters.push(headerFilter);
  }
};
const removeHeaderFilterValue = (headerFilter: HeaderFilterValue) => {
  let headerFilters = state.headerFilters as HeaderFilterValue[];
  let index = headerFilters.indexOf(headerFilter);
  headerFilters.splice(index, 1);
};

// 清空所有过滤条件
const clearAllFilters = () => {
  ++tableContext.filterUpdateCount;
  state.headerFilters.splice(0, state.headerFilters.length);
};

// 当前选择
const checkedRows = reactive([]);

// 表格字段存储
const columnStorage = computed((): TableColumnStorage => {
  return props.columnStorage as TableColumnStorage;
});

// 是否远程存储字段
const isRemoteStorage = computed((): boolean => {
  return columnStorage.value && columnStorage.value.mode == "remote";
});

// 返回el-table生效的字段数组
const elTableColumns = computed(() => {
  return state.tableColumns.filter(
    col => col.hidden != true && col.hidden != "true"
  );
});

/**
 * 表格绑定的数据
 */
const elTableData = computed(() => {
  if (state.localFilterData) {
    return state.localFilterData;
  }
  // if (tableStates.value.memoryPage) {
  //   // 需要计算分页
  //   return state.tableData;
  // }
  return state.tableData;
});

// 计算属性
const elTableProps = computed(() => {
  let {
    url,
    query,
    method,
    center,
    columns,
    data,
    tableAlert,
    columnSortable,
    autoRequest,
    pagination,
    toolbar,
    operator,
    loading,
    enhanced,
    showCheckbox,
    showIndex,
    tableProps,
    style,
    ...otherProps
  } = props;
  return {
    ...otherProps,
    ...(tableProps || {})
  };
});

const { showCheckbox, showIndex } = toRefs(props);
const tableAlertProps = computed((): TableAlert => {
  let { className, ...alertProps } = props.tableAlert || {};
  return alertProps;
});

const getColumnClassName = (column: ImTableColumnProps<any>) => {
  if (props.columnSortable) {
    let classNames = [dragColumnClass];
    if (column.disableColumnSortable || column.fixed) {
      classNames.push(disableDragColumnClass);
    }
    return classNames.join(" ");
  }
  return "";
};

const tableLoading = computed(() => {
  return props.loading || state.requestLoading;
});

// 分页属性区分外部传入的分页模型还是内置模型
const paginationProps = computed<ImTablePaginationProps>(() => {
  let { pagination } = props;
  if (typeof pagination == "boolean") {
    return state.pagination;
  } else {
    return pagination;
  }
});

// 无数据时隐藏分页
const hidePaginationIfEmpty = computed(() => {
  return (
    paginationProps.value.hideOnEmptyData !== false &&
    paginationProps.value.total == 0
  );
});

const operatorColumn = computed<ImTableColumnProps<any>>(() => {
  let { operator } = props;
  if (typeof operator == "boolean") {
    return {
      label: "操作",
      fixed: "right",
      slot: "operator"
    } as ImTableColumnProps<any>;
  } else {
    let newOperator = { ...operator };
    if (newOperator.fixed === undefined) {
      newOperator.fixed = "right";
    }
    newOperator.slot = "operator";
    return newOperator;
  }
});
const toolbarState = reactive(
  (props.toolbar as TableToolbar)?.initialState || {}
);
const toolbarSlotScope = computed<ImToolbarSlot>(() => {
  return {
    checkedRows,
    instance,
    pagination: paginationProps.value,
    toolbarState,
    size: props.size
  };
});

const loadMemoryPage = () => {};

const loadOnPageChange = () => {
  if (tableStates.value.memoryPage) {
    loadMemoryPage();
  } else {
    loadRequest();
  }
};

const reload = () => {
  if (tableStates.value.showPage) {
    let page = paginationProps.value;
    emits("on-reload", page.currentPage || 1, page.pageSize);
  } else {
    emits("on-reload");
  }
  loadRequest();
  return instance;
};

const showSetting = () => {
  state.drawerVisible = true;
  // emits("on-setting");
};

const handleDrawerClose = () => {
  state.drawerVisible = false;
  triggerColumnStorage();
};

const triggerColumnStorage = () => {
  emits("on-columns-storage", state.tableColumns);
  if (props.columnStorage) {
    saveStorageCustomTableSet();
  }
};

// 分页大小变化事件
const onPageSizeChange = val => {
  state.pagination.currentPage = 1;
  // 兼容2.0
  emits("on-pagesize-change", 1, val, props.query);
  // 3.0
  emits("on-page-size-change", 1, val, props.query);
  // 无论时pageNum还是pageSize可以统一使用on-page-change
  emits("on-page-change", val, paginationProps.value.pageSize, props.query);
  loadOnPageChange();
};

// 分页页码变化事件
const onPageChange = val => {
  // 兼容2.0
  emits("on-page-load", val, paginationProps.value.pageSize, props.query);
  // 3.0
  emits(
    "on-page-current-change",
    val,
    paginationProps.value.pageSize,
    props.query
  );
  // 无论时pageNum还是pageSize可以统一使用on-page-change
  emits("on-page-change", val, paginationProps.value.pageSize, props.query);
  loadOnPageChange();
};

// 表格状态信息
const tableStates = computed<TableInternalState>(() => {
  let {
    enhanced,
    tableAlert,
    toolbar,
    pagination,
    operator,
    request,
    autoRequest,
    columnSortable
  } = props;
  let showToolbar = !!toolbar;
  const showPage = !!pagination;
  const paginationStyle: CSSProperties = {};
  let memoryPage = false;
  if (showPage) {
    if (typeof pagination == "object") {
      const paginationOption = pagination as ImTablePaginationProps;
      memoryPage = paginationOption.memory;
      if (paginationOption.align) {
        paginationStyle.justifyContent = paginationOption.align;
      }
    }
  }
  return {
    enhanced,
    showAlert: tableAlert != false,
    showToolbar,
    reloadBar:
      showToolbar &&
      (toolbar == true || (toolbar as TableToolbar).reload != false),
    settingBar:
      showToolbar &&
      (toolbar == true || (toolbar as TableToolbar).setting != false),
    showPage: pagination != false,
    memoryPage,
    paginationStyle,
    showOperator: !!operator,
    useRequest: typeof request == "function",
    autoRequest,
    columnSortable:
      typeof columnSortable == "boolean"
        ? columnSortable
        : (columnSortable as SortableOption).enable != false,
    columnSortableOption:
      typeof columnSortable == "boolean"
        ? ({} as SortableOption)
        : (columnSortable as SortableOption)
  };
});

const reloadComponent = computed(() => {
  if (typeof props.toolbar == "boolean") {
    return null;
  }
  let bar = props.toolbar as TableToolbar;
  return bar.reload != true && bar.reload;
});
const settingComponent = computed(() => {
  if (typeof props.toolbar == "boolean") {
    return null;
  }
  let bar = props.toolbar as TableToolbar;
  return bar.setting != true && bar.setting;
});

const getLocalColumnOptions = (column: ImTableColumnProps<any>) => {
  let tableData = state.tableData || [];
  let options = [];
  let values = [];
  for (let row of tableData) {
    const value = row[column.prop];
    if (!values.includes(value)) {
      values.push(value);
      options.push({
        label: column.getOptionText
          ? column.getOptionText(column.prop, row, column)
          : value,
        value
      });
    }
  }
  return options;
};

const doFilterLocal = (values: any[], column: ImTableColumnProps<any>) => {
  if (!values || values.length == 0) {
    state.localFilterData = null;
  } else {
    state.localFilterData = state.tableData.filter(row => {
      return values.includes(row[column.prop]);
    });
  }
};
const doFilterEmits = (
  params: any,
  value: any,
  column: ImTableColumnProps<any>
) => {
  // 这里标记下，在数据刷新后保留当前过滤的选项
  state.emitsFilterFlag = true;
  emits("on-filter", params, value, column);
};

const tableContext: TableContext = reactive({
  filterUpdateCount: 0,
  currentSort: null,
  currentFilter: null,
  getLocalColumnOptions,
  doFilterLocal,
  doFilterEmits,
  getHeaderFilterValues,
  addHeaderFilterValue,
  removeHeaderFilterValue,
  clearAllFilters
});
const clearCurrentFilter = () => {
  tableContext.currentFilter = null;
  return instance;
};

const clearContextOnDataChange = () => {
  // 清除本地过滤数据
  state.localFilterData = null;
  if (!state.emitsFilterFlag) {
    if (state.headerFilters.length == 0) {
      clearCurrentFilter();
      ++tableContext.filterUpdateCount;
    }
  } else {
    state.emitsFilterFlag = false;
  }
};

const setFilterOptions = (prop: string, options: any[]) => {
  let targets: ImTableColumnProps<any>[] = [];
  filterColumnsByProp(prop, props.columns, targets);
  for (let column of targets) {
    if (column.meta) {
      column.meta.options = options;
    } else {
      column.meta = {
        filterType: "select",
        options
      };
    }
  }
  return instance;
};

const handleSelectionChange = (newSelection: any[]) => {
  checkedRows.splice(0, checkedRows.length, ...newSelection);
  emits("selection-change", newSelection);
};

const getTable = () => {
  return table.value;
};
const getOffsetIndex = () => {
  if (!tableStates.value.showPage) return 0;
  let { currentPage = 1, pageSize } = paginationProps.value;
  return (currentPage - 1) * pageSize;
};
const getSize = () => {
  return props.size as string;
};

const getData = () => {
  return state.tableData;
};
const loadPage = (page: number) => {
  paginationProps.value.currentPage = Math.max(parseInt(page), 1);
  if (tableStates.value.memoryPage) {
  } else {
    loadRequest();
  }
  return instance;
};

const currentPage = (page: number) => {
  paginationProps.value.currentPage = Math.max(page, 1);
  return instance;
};

// 表格实例
const instance: ImTableInstance = <ImTableInstance>{
  getTable,
  getData,
  defaultAlign() {
    return state.defaultAlign;
  },
  getOffsetIndex,
  getSize,
  tableStates: tableStates.value,
  toolbarState,
  checkedRows,
  reload,
  clearCurrentFilter,
  setFilterOptions,
  loadPage,
  currentPage,
  clearAllFilters,
  getFilterValues: getHeaderFilterValues
};

const mergeInstance = () => {
  // lazy merge
  Object.assign(instance as any, table.value);
};

const loadRequest = () => {
  if (tableStates.value.useRequest) {
    let params: RequestParams = {};
    let showPage = tableStates.value.showPage;
    if (showPage) {
      let { currentPage = 1, pageSize } = paginationProps.value;
      params.currentPage = currentPage;
      params.pageSize = pageSize;
    }
    let promise: Promise<RequestResult> = props.request(params);
    state.requestLoading = true;
    promise
      .then(res => {
        let { rows, total, success } = res;
        if (success) {
          state.tableData = rows || [];
          if (showPage) {
            paginationProps.value.total = total;
          }
        } else {
          console.error("error request ", res);
        }
      })
      .catch(err => {
        console.error("error: ", err);
      })
      .finally(() => {
        state.requestLoading = false;
      });
  } else {
    console.warn("request method not defined");
  }
};

const mergeCustomSet = (
  columns: ImTableColumnProps<any>[],
  customSet: CustomTableSet
) => {
  if (!customSet || Object.keys(customSet).length == 0) return columns;
  let { defaultAlign, pagination, columnsStates } = customSet;
  state.defaultAlign = defaultAlign;
  if (pagination) {
    paginationProps.value.align = pagination.align;
  }
  if (!columnsStates || columnsStates.length == 0) {
    return columns;
  }
  const map = {};
  let order = 0;
  for (let columnsState of columnsStates) {
    let { id } = columnsState;
    map[id] = {
      columnsState,
      order: ++order
    };
  }
  // 遍历全量数组
  for (let column of columns) {
    let { id } = column;
    if (id) {
      let { columnsState } = map[id] || {};
      if (columnsState) {
        const { id, ...props } = columnsState;
        Object.assign(column, { ...props });
      }
    } else {
      column.id = genId();
    }
  }
  // 排序
  columns.sort((c1, c2) => {
    let { order: order1 } = map[c1.id] || {};
    let { order: order2 } = map[c2.id] || {};
    return order1 > order2 ? 1 : -1;
  });
  return columns;
};

const buildCustomTableSet = (): CustomTableSet => {
  return {
    defaultAlign: state.defaultAlign as any,
    pagination: {
      align: paginationProps.value.align
    },
    columnsStates: state.tableColumns.map(tableColumn => {
      let { id, hidden, fixed, filterable, sortable } = tableColumn;
      // 统一转化为boolean
      const result = {
        id,
        hidden: !!hidden,
        fixed: !!fixed,
        filterable,
        sortable: !!sortable
      };
      // if (hidden) {
      //   result["hidden"] = true;
      // }
      // if (fixed) {
      //   result["fixed"] = true;
      // }
      // if (filterable) {
      //   result["filterable"] = true;
      // }
      // if (sortable) {
      //   result["sortable"] = true;
      // }
      return result as ColumnsState;
    })
  };
};

// 加载表格字段
const loadRemoteTableColumns = (useDefault?: boolean) => {
  if (isRemoteStorage.value) {
    // 远程加载
    let { getTableSet, tableCode, instance, mapColumnFn } = columnStorage.value;
    if (!getTableSet) {
      getTableSet = globalSet.columnStorge?.getTableSet;
    }
    if (!getTableSet) {
      console.error(
        "没有配置远程获取字段的函数请在全局设置或者在表格属性column-storage中设置"
      );
      return;
    }
    getTableSet(tableCode, instance).then(res => {
      let { columns, customTableSet, customTableInfo } = res;
      if (typeof mapColumnFn == "function" && Array.isArray(columns)) {
        columns = columns.map(mapColumnFn);
      }
      // 合并个性化配置到字段全量表格字段列表
      state.tableColumns = mergeCustomSet(
        columns || [],
        useDefault ? null : customTableSet
      );
      state.customInfo = customTableInfo;
      if (useDefault) {
        handleStorageCustomTableSet();
      }
      updateColumnSortableHandler();
    });
  }
};

const handleStorageCustomTableSet = (waitResponse?) => {
  let customTableSet = buildCustomTableSet();
  if (isRemoteStorage.value) {
    let { saveTableSet, tableCode, instance } = columnStorage.value;
    if (!saveTableSet) {
      saveTableSet = globalSet.columnStorge?.saveTableSet;
    }
    if (saveTableSet) {
      let response = saveTableSet({
        tableCode,
        instance,
        customTableSet,
        customTableInfo: state.customInfo
      });
      if (waitResponse) {
        response.then(res => {
          ElMessage.success("已保存");
        });
      }
    } else {
      console.error(
        "没有配置远程获取字段的函数请在全局设置或者在表格属性column-storage中设置"
      );
      return;
    }
  } else {
    // 本地存储
    if (columnStorage.value) {
      let { tableCode } = columnStorage.value;
      // 从浏览器中获取
    }
  }
};

// 延迟保存（关闭窗口）
const saveStorageCustomTableSet = debounce(handleStorageCustomTableSet, 1500);

const loadTableData = () => {
  let { useRequest, autoRequest } = tableStates.value;
  if (useRequest) {
    if (autoRequest) {
      loadRequest();
    }
  } else {
    state.tableData = props.data || [];
  }
};

const updateColumnSortableHandler = () => {
  if (!tableWrap.value) return;
  if (state.columnSortableHandler != null) {
    if (tableStates.value.columnSortable) {
      state.columnSortableHandler.update();
    } else {
      state.columnSortableHandler.destroy();
      state.columnSortableHandler = null;
    }
  } else {
    if (tableStates.value.columnSortable) {
      setTimeout(() => {
        try {
          const container = tableWrap.value.querySelector(
            ".el-table__header-wrapper tr"
          ) as HTMLElement;
          let columnSortableOption = tableStates.value.columnSortableOption;
          let sortOption: SortableOption = {
            ...columnSortableOption,
            filterClassName: dragColumnClass,
            disableClassName: disableDragColumnClass,
            onDrop: (fromIndex: number, toIndex: number, e: DragEvent) => {
              if (columnSortableOption.onDrop) {
                try {
                  columnSortableOption.onDrop(fromIndex, toIndex, e);
                } catch (e) {}
              }
              // 将fromIndex所在的列数据插到toIndex位置
              let displayColumns = elTableColumns.value;
              let fromColumn = displayColumns[fromIndex],
                toColumn = displayColumns[toIndex];
              if (fromIndex != toIndex && fromColumn && toColumn) {
                let columns = state.tableColumns;
                let originalFromIndex = columns.indexOf(fromColumn);
                let originalToIndex = columns.indexOf(toColumn);
                emits(
                  "on-column-sort-change",
                  originalFromIndex,
                  originalToIndex
                );
                const element = columns.splice(originalFromIndex, 1)[0];
                columns.splice(originalToIndex, 0, element);
                triggerColumnStorage();
                emits("on-sort-finished");
                nextTick(() => {
                  state.columnSortableHandler.update();
                });
              }
            }
          };
          state.columnSortableHandler = createSortable(container, sortOption);
        } catch (error) {
          console.trace(error);
        }
      }, 0);
    }
  }
};

const updateDataSortableHandler = () => {
  if (!tableWrap.value) return;
  if (state.dataSortableHandler != null) {
    if (props.sortable) {
      state.dataSortableHandler.update();
    } else {
      state.dataSortableHandler.destroy();
      state.dataSortableHandler = null;
    }
  } else {
    if (props.sortable) {
      setTimeout(() => {
        let sortableOption =
          typeof props.sortable == "object" ? props.sortable : {};
        try {
          const container = tableWrap.value.querySelector(
            ".el-table__body-wrapper .el-table__body tbody"
          ) as HTMLElement;
          let sortOption: SortableOption = {
            ...sortableOption,
            onDrop: (fromIndex: number, toIndex: number, e: DragEvent) => {
              let displayTableDatas = elTableData.value;
              let fromRow = displayTableDatas[fromIndex],
                toRow = displayTableDatas[toIndex];
              if (fromIndex != toIndex && fromRow && toRow) {
                let tableData = state.tableData;
                let originalFromIndex = tableData.indexOf(fromRow);
                let originalToIndex = tableData.indexOf(toRow);
                if (originalFromIndex > -1 && originalToIndex > -1) {
                  const element = tableData.splice(originalFromIndex, 1)[0];
                  tableData.splice(originalToIndex, 0, element);
                  emits("on-data-sort-change");
                  nextTick(() => {
                    state.dataSortableHandler.update();
                  });
                }
              }
            }
          };
          state.dataSortableHandler = createSortable(container, sortOption);
        } catch (error) {
          console.trace(error);
        }
      }, 0);
    }
  }
};

const bindEvents = () => {
  if (tableWrap.value) {
    tableWrap.value
      .querySelector(".im-inner-table")
      .addEventListener("dragover", stopAndPreventDefault, true);
  }
};

const unbindEvents = () => {
  if (tableWrap.value) {
    tableWrap.value
      .querySelector(".im-inner-table")
      .removeEventListener("dragover", stopAndPreventDefault, true);
    if (state.columnSortableHandler) {
      try {
        state.columnSortableHandler.destroy();
      } catch (e) {}
    }
    if (state.dataSortableHandler) {
      try {
        state.dataSortableHandler.destroy();
      } catch (e) {}
    }
  }
};

onMounted(() => {
  mergeInstance();
  // 判断是否远程加载表格字段
  if (props.columnStorage) {
    if (isRemoteStorage.value) {
      loadRemoteTableColumns();
    } else {
      // 加载本地存储的字段
    }
  }
  // 加载表格数据
  loadTableData();
  // init
  nextTick(() => {
    updateColumnSortableHandler();
    bindEvents();
  });
});
onBeforeMount(() => {
  unbindEvents();
});

provide("slots", slots);
provide("tableContext", tableContext);
provide("tableEmits", emits);
provide("table", instance);
provide("tableProps", props);

watch(
  () => props.columns,
  val => {
    state.tableColumns = val || [];
    state.tableColumns.forEach(column => {
      if (!column.id) {
        column.id = genId();
      }
    });
    updateColumnSortableHandler();
  },
  {
    immediate: true
  }
);
watch(
  () => elTableColumns.value.length,
  () => {
    nextTick(updateColumnSortableHandler);
  }
);
watch(
  () => props.data,
  val => {
    clearContextOnDataChange();
    if (!tableStates.value.useRequest) {
      state.tableData = val || [];
    }
  },
  {
    immediate: true
  }
);

watch(
  () => elTableData.value,
  () => {
    checkedRows.splice(0, checkedRows.length);
    nextTick(updateDataSortableHandler);
  },
  { immediate: true }
);

defineExpose(instance);
</script>

<style lang="scss">
.im-table {
  th.el-table__cell {
    background-color: #fafafa;
    color: #000000d9;
  }
  .el-button.is-text {
    margin-left: 4px;
    padding-left: 4px;
    padding-right: 4px;
  }
}

html.dark .im-table .el-table th.el-table__cell {
  background-color: #1d1d1d !important;
  color: #fff;
}
</style>

<style lang="scss" scoped>
.im-table {
  position: relative;
  :deep(.el-drawer__header) {
    margin-bottom: 0px !important;
  }
  .im-table-toolbar {
    padding: 0 2px;
    display: flex;
    line-height: 32px;
    margin-bottom: 10px;
    .left,
    .center {
      display: flex;
      align-items: center;
      flex: 1;
      gap: 2px;
    }

    .left {
      justify-content: flex-start;
    }

    .center {
      justify-content: flex-end;
    }

    .right {
      display: flex;
      align-items: center;
    }
  }

  .im-table-alert {
    padding: 6px 16px;
    margin-top: 5px;
    margin-bottom: 10px;
    border: 1px solid var(--el-color-primary-light-7) !important;
    background-color: var(--el-color-primary-light-9) !important;
    :deep(.el-alert__description) {
      font-size: 12px;
      color: var(--el-color-primary) !important;
    }
    :deep(.el-alert__close-btn) {
      top: 6px !important;
    }
    .alert-content {
      display: flex;
      align-items: center;
    }
  }

  .im-table-pagination {
    margin: 20px 2px 5px 2px !important;
    overflow: hidden;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
