<template>
  <avue-crud
    :data="tableData"
    :option="tableOption"
    v-model:page="tablePage"
    :table-loading="tableLoading"
    @refresh-change="resetTablePageAndQuery"
    @size-change="loadTaskData"
    @current-change="loadTaskData"
    @selection-change="selectionChangeHandler"
  >
    <!-- 表格左侧菜单 -->
    <template #menu-left>
      <el-button type="primary" :icon="useRenderIcon('EP-Plus')"
                 v-auth="'vul:task:add'"
                 @click="openEditTaskDialog({})"
      >
        新建任务
      </el-button>
    </template>
    <!-- 表格右侧菜单 -->
    <template #menu-right="{ size }">
      <div class="float-left flex-sc pr-3 pt-0.5 gap-3">
        <el-input
          clearable
          placeholder="任务名称"
          v-model="searchCondition.name"
          :size="size"
          @blur="searchCondition.name = ($event.target as HTMLInputElement).value.trim()"
        >
          <template #append>
            <el-button
              :icon="useRenderIcon('EP-Search')"
              @click="resetTablePageAndQuery"
            />
          </template>
        </el-input>
        <el-select
          v-model="vulScanTools"
          placeholder="任务类型"
          clearable
          multiple
          collapse-tags
          filterable
          style="width: 330px"
          class="ml-3"
          @change="resetTablePageAndQuery"
        >
          <el-option
            v-for="item in taskTypeData"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-tooltip
          content="批量删除任务"
          placement="top"
          :open-delay="1000"
        >
          <el-button
            :icon="useRenderIcon('EP-DeleteFilled')"
            circle
            :disabled="selectedTasks?.length==0"
            :size="size"
            v-auth="'vul:task:delete'"
            @click="batchDeleteTaskHandler"
          />
        </el-tooltip>
      </div>
    </template>
    <!-- 表格操作按钮 -->
    <template #menu="{ row, size, type }">
      <el-button
        :size="size"
        :type="type"
        :icon="useRenderIcon('EP-View')"
        text
        :disabled="row.runningStatus == 1"
        v-auth="'vul:task:result'"
        @click="viewResultHandler(row)"
      >
        查看结果
      </el-button>
      <el-button
        :size="size"
        :type="type"
        :icon="useRenderIcon('EP-Edit')"
        text
        :disabled="row.runningStatus == 1"
        v-auth="'vul:task:edit'"
        @click="openEditTaskDialog(row)"
      >
        编辑
      </el-button>
      <el-button
        :size="size"
        type="danger"
        :icon="useRenderIcon('EP-Delete')"
        text
        :disabled="row.runningStatus == 1"
        v-auth="'vul:task:delete'"
        @click="deleteTaskHandler(row)"
      >
        删除
      </el-button>
    </template>
    <template #progress="{row}">
      <el-tag :type="getVulTaskProcessTagType(row.progress)">{{ getVulTaskProcessLabel(row.progress) }}</el-tag>
    </template>
    <template #vulScanTool="{row}">
      <el-tag type="primary" plain>{{ getTaskTypeLabel(row.vulScanTool) }}</el-tag>
    </template>
    <template #total="{row}">
      <el-text :type="row.total>0?'danger':'success'" class="font-bold">{{ row.total }}</el-text>
    </template>
    <template #criticalCnt="{row}">
      <el-text :type="row.criticalCnt>0?'danger':'success'" class="font-bold">{{ row.criticalCnt }}</el-text>
    </template>
    <template #highCnt="{row}">
      <el-text :type="row.highCnt>0?'danger':'success'" class="font-bold">{{ row.highCnt }}</el-text>
    </template>
    <template #midCnt="{row}">
      <el-text :type="row.midCnt>0?'danger':'success'" class="font-bold">{{ row.midCnt }}</el-text>
    </template>
    <template #lowCnt="{row}">
      <el-text :type="row.lowCnt>0?'danger':'success'" class="font-bold">{{ row.lowCnt }}</el-text>
    </template>
  </avue-crud>
</template>

<script lang="ts" setup>
import {computed, getCurrentInstance, h, reactive, ref, toRefs} from 'vue';
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {addDialog, closeAllDialog} from "@/components/ReDialog/index";
import VulTaskEditor from "@/views/modules/eam/assetMap/taskManage/components/VulTaskEditor.vue";
import {deleteVulTask, queryTaskData, queryVulTaskType} from "@/views/modules/eam/assetMap/taskManage/api/vulTaskApi";
import {ResultStatus} from "@/utils/http/types";
import {getVulTaskProcessLabel, getVulTaskProcessTagType} from "@/views/modules/eam/assetMap/taskManage/util/task_data";

const {$message, $confirm} = getCurrentInstance().appContext.config.globalProperties;

//编辑任务 Ref
const editTaskRef = ref<InstanceType<typeof VulTaskEditor>>();

// 定义事件
const emit = defineEmits(["jump-to", "select-task"]);

//数据对象
const state = reactive({
  tableLoading: false,
  searchCondition: {
    name: "",
    vulScanTool: null
  },
  taskTypeData: [],
  vulScanTools: [],
  tableData: [],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  selectedTasks: [] as Array<string>,
})
const {
  tableLoading,
  searchCondition,
  taskTypeData,
  vulScanTools,
  tableData,
  tablePage,
  selectedTasks
} = toRefs(state)

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 320;
});

//表格选项
const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  selectable: (row: any) => row.runningStatus != 1,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 200,
  height: tableHeight,
  rowKey: "id",
  column: [
    {
      label: "任务名称",
      prop: "name",
      sortable: true,
      overHidden: true
    },
    {
      label: "任务类型",
      prop: "vulScanTool",
    },
    {
      label: "执行周期",
      prop: "schedulingCycleName",
    },
    {
      label: "进度",
      prop: "progress",
      width: 220
    },
    {
      label: "漏洞总数",
      prop: "total",
    },
    {
      label: "危急数量",
      prop: "criticalCnt",
    },
    {
      label: "高危数量",
      prop: "highCnt",
    },
    {
      label: "中危数量",
      prop: "midCnt",
    },
    {
      label: "低危数量",
      prop: "lowCnt",
    },
    {
      label: "最近运行时间",
      prop: "startTime",
      width: 150
    }
  ]
});

// 加载任务类型数据
const loadTaskTypeData = async () => {
  const {data} = await queryVulTaskType();
  if (data && data.length > 0) {
    state.taskTypeData = data.map((item: any) => ({
      label: item.name,
      value: item.code
    }))
  } else {
    state.taskTypeData = [];
  }
}
loadTaskTypeData();

//获取漏扫类型Label
const getTaskTypeLabel = (type: string) => {
  const match = state.taskTypeData.find(item => item.value === type);
  return match ? match.label : '-';
}


// 打开编辑任务窗口
const openEditTaskDialog = (t: any) => {
  addDialog({
    title: t.name ? `编辑任务 - ${t.name}` : "新建任务",
    width: "80%",
    fullscreenIcon: true,
    closeOnClickModal: false,
    props: {taskInfo: t, taskTypeData: state.taskTypeData},
    contentRenderer: () => h(VulTaskEditor, {
      ref: editTaskRef, onSuccess: () => {
        closeAllDialog();
        loadTaskData();
      }
    }),
    beforeSure(done) {
      editTaskRef.value.submitData();
    },
  });
}

//重置分页后查询数据
const resetTablePageAndQuery = () => {
  state.tablePage.currentPage = 1;
  loadTaskData();
}

//加载任务数据
const loadTaskData = async () => {
  state.tableLoading = true;
  state.tableData = [];
  //组装查询条件
  const conditions = {
    ...state.searchCondition,
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize,
  }
  if (state.vulScanTools && state.vulScanTools.length > 0) {
    conditions.vulScanTool = state.vulScanTools.join(",");
  }

  //查询数据
  const res = await queryTaskData(conditions);
  if (res.status === ResultStatus.Success) {
    state.tableData = res.data.rows;
    state.tablePage.total = res.data.totalElements;
  }
  state.tableLoading = false;
}
loadTaskData();

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.taskallid));
  state.selectedTasks = selectIds;
}

//查看结果触发
const viewResultHandler = (t: any) => {
  emit("select-task", t);
  jumpTo('vulTaskResult');
}

//删除任务触发
const deleteTaskHandler = async (t: any) => {
  $confirm(
    `您确定要删除 '${t.name}' 么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    const {status} = await deleteVulTask({taskallid: t.taskallid});
    if (status === ResultStatus.Success) {
      $message({
        type: "success",
        message: "已成功删除任务！"
      });
      await loadTaskData();
    }
  });
}

// 批量删除任务触发
const batchDeleteTaskHandler = async () => {
  const selectedSize = state.selectedTasks.length;
  $confirm(
    `您确定要删除已选择的 ${selectedSize} 项任务么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    const {status} = await deleteVulTask({taskallid: state.selectedTasks.join(",")});
    if (status === ResultStatus.Success) {
      $message({
        type: "success",
        message: `已成功删除 ${selectedSize} 项任务！`
      });
      await loadTaskData();
    }
  });
}

// 跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};

</script>
