<template>
  <el-drawer
    v-model="drawerVisible"
    :before-close="cancel"
    :show-close="true"
    :destroy-on-close="true"
    class="ai-asset-analysis-drawer"
    size="60%"
    header-class="my-ai-header"
  >
    <template #header>
      <div class="flex-bc">
        <el-page-header @back="cancel">
          <template #content>
            <div class="flex justify-between">
              <span class="mr-3 font-bold"> AI智能评估 </span>
            </div>
          </template>
        </el-page-header>
        <div
          style="
            margin-right: 10px;
            display: flex;
            gap: 8px;
            align-items: center;
          "
        >
          <el-select
            v-if="state.ipAddressList.length > 1"
            v-model="state.ipAddress"
            filterable
            placeholder="请选择IP"
            @change="call"
          >
            <el-option
              v-for="(ipAddress, index) in state.ipAddressList"
              :key="index"
              :label="ipAddress"
              :value="ipAddress"
            ></el-option>
          </el-select>
          <el-segmented
            v-model="state.dateRangeSign"
            :options="state.timeSegmentOptions"
            @change="call"
          >
          </el-segmented>
        </div>
      </div>
    </template>
    <div class="ai-asset-analysis-main">
      <el-button
        class="collapse-btn"
        type="primary"
        link
        :icon="FullScreen"
        :title="state.collapseFlag ? '显示树图' : '隐藏树图'"
        @click="state.collapseFlag = !state.collapseFlag"
      >
      </el-button>
      <div v-if="state.collapseFlag" class="asset-desc">
        <span class="desc">{{ state.assetChartInfo.message }}</span>
      </div>
      <div
        class="asset-ai-chart-wrap"
        v-else
        style="height: 420px; width: 100%; border-radius: 4px"
      >
        <ChartComponent :option="state.option"> </ChartComponent>
      </div>
      <div
        v-if="state.ipAddress && !state.collapseFlag"
        class="asset-desc asset-desc2"
      >
        <span class="desc"
          >对资产IP {{ state.ipAddress }} 进行AI智能评估分析:</span
        >
      </div>
      <el-divider></el-divider>
      <div
        ref="mailEl"
        class="ai-analysus-content-wrap"
        :style="mailAiContentStyle"
      >
        <div class="ai-analysus-content">
          <AiMarkdown
            :markdown-text="state.message"
            :show-ai-generate-desc="!state.thinking"
            ai-generate-desc="AI生成内容，仅供参考学习"
          >
            <template #extra="{ message }">
              <div
                v-if="!state.thinking"
                class="message-btn"
                style="
                  display: flex;
                  gap: 4px;
                  position: absolute;
                  right: 5px;
                  top: 4px;
                "
              >
                <el-button type="primary" link :icon="Refresh" @click="callAi">
                  重新回答
                </el-button>
                <el-button
                  type="primary"
                  link
                  :icon="CopyDocument"
                  @click="handleCopy(message)"
                >
                  复制
                </el-button>
              </div>
            </template>
          </AiMarkdown>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  nextTick,
  onBeforeMount,
  reactive,
  ref,
  toRefs,
  watch
} from "vue";
import AiMarkdown from "@/views/modules/ai/components/AiMarkdown.vue";
import { getAiInfoByCode, ragChar } from "@/views/modules/ai/api";
import { CopyDocument, Refresh, FullScreen } from "@element-plus/icons-vue";
import { copyHtmlToStyleText } from "@/utils/copy";
import { ElMessage } from "element-plus";
import { queryAssetTreeChartByIp } from "@/views/modules/eam/zcgl/instance/source/api/assetAi";
import dayjs from "dayjs";
import ChartComponent from "@/components/Echarts/ChartComponent.vue";

const mailEl = ref<HTMLDivElement>();
const { $confirm } = getCurrentInstance().appContext.config.globalProperties;
//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  assetInfo: {
    type: Object,
    default: () => {}
  }
});

//数据对象
const state = reactive({
  drawerVisible: false,
  thinking: false,
  handleCancel: null,
  question: "",
  message: "",

  ak: null,

  ipAddress: null,
  ipAddressList: [],
  dateRangeSign: "168h",
  timeSegmentOptions: [
    {
      label: "近24小时",
      value: "24h"
    },
    {
      label: "近7天",
      value: "168h"
    },
    {
      label: "近30天",
      value: "720h"
    }
  ],

  // 是否折叠
  collapseFlag: false,
  // 图表数据
  assetChartInfo: {
    message: "",
    treeData: []
  },
  option: {}
});
const { drawerVisible } = toRefs(state);
const emit = defineEmits(["update:visible"]);

const cancel = () => {
  if (state.thinking) {
    $confirm("AI正在思考回答中，确定要关闭吗?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "info"
    })
      .then(() => {
        stopReplayMessage();
        emit("update:visible", false);
      })
      .catch(() => {});
  } else {
    emit("update:visible", false);
  }
};

const scrollToBottom = () => {
  nextTick(() => {
    if (mailEl.value) {
      let mainNode = mailEl.value as HTMLDivElement;
      mainNode.scrollTop = mainNode.scrollHeight;
    }
  });
};

const stopReplayMessage = async (onlyStop?) => {
  let cancel = state.handleCancel;
  if (cancel) {
    // 取消是异步操作
    cancel("用户手动终止");
  }
  state.thinking = false;
  if (!onlyStop) {
    nextTick(() => {
      scrollToBottom();
    });
  }
};

const finishReplayMessage = () => {
  state.thinking = false;
};

const mailAiContentStyle = computed((): CSSProperties => {
  if (state.collapseFlag) {
    return {
      height: "calc(100vh - 180px)"
    };
  }
  return {
    height: "calc(100vh - 600px)"
  };
});

const callQueryParams = computed(() => {
  return {
    ip: state.ipAddress,
    dateRange: state.dateRangeSign,
    endTime: dayjs().format("YYYY-MM-DD HH:mm:ss")
  };
});

const callAi = async () => {
  // 一般在未思考模式下，如果正在思考先停止
  if (state.thinking || state.handleCancel) {
    stopReplayMessage(true);
  }
  state.message = "智能评估中...";
  // 构建axios取消句柄
  const cancelFunc = function executor(c) {
    // 保存取消函数
    state.handleCancel = c;
  };
  // 如果有取消的需要确保取消成功
  if (state.handleCancel) {
    // 存在请求未取消,取消调用后onDownloadProgress依然会继续执行, 如果放开会出现两个请求短暂同时消费后台的数据，导致回答内容乱串
    // 使用轮询判断上一个请求是否完成(取消或者正常完成)，这里暂时没有好的处理方案
    await new Promise(resolve => {
      let interval = setInterval(() => {
        if (!state.handleCancel) {
          resolve([]);
          clearInterval(interval);
        }
      }, 10);
    });
  }
  // 强制开始
  state.thinking = true;

  ragChar(
    state.ak,
    callQueryParams.value,
    ({ event }) => {
      // 处理异步取消延迟带来的问题
      const list = event.target.responseText.split("\n\n");
      let text = "";
      let isRun = true;
      list.forEach((i: any) => {
        if (i.startsWith("data:Error")) {
          isRun = false;
          text += i.substring(5, i.length);
          // chatStore.updateMessage(aiChatId.value, text, true);
          return;
        }
        if (!i.startsWith("data:{")) {
          return;
        }
        const dataStr = i.substring(5, i.length);
        try {
          const { done, message } = JSON.parse(dataStr);
          if (done || message === null) {
            isRun = false;
            finishReplayMessage();
            return;
          }
          text += message;
          scrollToBottom();
        } catch (e) {
          console.trace(e);
        }
      });
      if (text) {
        // end
        state.message = text;
      }
      if (!isRun) {
        scrollToBottom();
        return;
      }
    },
    cancelFunc
  )
    .catch(err => {
      if (err?.name === "CanceledError") {
        console.log("请求已取消");
      } else {
        state.message = "服务器繁忙，请稍后再试。";
      }
      return;
    })
    .finally(() => {
      // 请求完成或者取消完成
      state.handleCancel = null;
      finishReplayMessage();
    });

  nextTick(() => {
    scrollToBottom();
  });
};

const handleCopy = message => {
  let error = copyHtmlToStyleText(message);
  if (!error) {
    ElMessage.success("文本已复制到剪贴板！");
  } else {
    ElMessage.error("复制失败: " + error);
  }
};

const buildChartOption = () => {
  state.option = {
    // tooltip: {
    //   trigger: "item",
    //   triggerOn: "mousemove"
    // },
    series: [
      {
        type: "tree",
        data: state.assetChartInfo.treeData,
        top: "2%",
        left: "10%",
        bottom: "2%",
        right: "15%",
        symbolSize: 10,
        label: {
          position: "left",
          verticalAlign: "middle",
          align: "left",
          fontSize: 12,
          color: "var(--chart-label-color)"
        },
        symbol: "circle",
        // symbol: (value, params: Object) => {
        //   console.log("params", params);
        //   return "circle";
        // },
        itemStyle: {
          color: "#FFFFFF", // 设置节点北京颜色为白色
          borderColor: "#D9001B"
        },
        leaves: {
          label: {
            position: "right",
            verticalAlign: "middle",
            align: "left"
          }
        },
        emphasis: {
          focus: "descendant"
        },
        expandAndCollapse: true,
        initialTreeDepth: 5,
        animationDuration: 550,
        animationDurationUpdate: 750
      }
    ]
  };
};

const autoSetLableOffset = (childNodes: any[], level = 0) => {
  let index = 0;
  for (let child of childNodes) {
    ++index;
    let { children } = child;
    if (Array.isArray(children) && children.length > 0) {
      if (level == 1 || index == 1) {
        child.label = {
          offset: [-15, -15]
        };
      } else {
        child.label = {
          offset: [-15, 15]
        };
      }
      autoSetLableOffset(children, level + 1);
    } else {
      Object.assign(child, {
        symbol: "diamond", // 所有节点设为菱形
        symbolSize: 10
      });
    }
  }
};

const call = async () => {
  // 查询图表
  try {
    let res = await queryAssetTreeChartByIp(callQueryParams.value);
    let { message, treeData } = res.data;
    state.assetChartInfo.message = message;
    state.assetChartInfo.treeData = Array.isArray(treeData)
      ? treeData
      : [treeData || {}];

    let datas = state.assetChartInfo.treeData;
    autoSetLableOffset(datas);

    let root = state.assetChartInfo.treeData[0];
    if (root) {
      root.label = {
        offset: [-50, -50]
      };
    }
    buildChartOption();
    // 调用ai
    callAi();
  } catch (e) {
    console.error(e);
  }
};

const initOnShow = () => {
  let { ipAddress } = props.assetInfo;
  const ipAddressList = ipAddress.split(",");
  state.ipAddressList = ipAddressList;
  state.ipAddress = ipAddressList[0];
};

watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
  initOnShow();
  call();
});

onBeforeMount(() => {
  getAiInfoByCode("asset_evaluate").then(res => {
    let resData = res.data;
    state.ak = resData.accessKey;
  });
});
</script>
<style lang="scss">
.ai-asset-analysis-drawer {
  background: linear-gradient(101deg, #d6eaff 0%, #e8ecef 100%);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04);
  border-radius: 16px 16px 16px 16px;
}

.dark {
  .ai-asset-analysis-main {
    background: #222020 !important;
  }

  .ai-asset-analysis-drawer {
    background: #222020 !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0px 1px 2px 0px rgba(255, 255, 255, 0.4);
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .asset-ai-chart-wrap {
    --chart-label-color: #fff !important;
    background: #0d1117 !important;
  }
}
</style>
<style lang="scss" scoped>
.ai-asset-analysis-main {
  transform: translateY(-20px);
  padding-top: 2px;
  background: #fff;
  .asset-desc {
    display: flex;
    gap: 10px;
    margin: 8px 0;
    padding-left: 10px;
    font-weight: bold;
    .desc {
      opacity: 0.8;
    }
  }

  .ai-analysus-content-wrap {
    height: calc(100vh - 180px);
    overflow: auto;
    width: calc(100%);
    padding-right: 10px;
    .ai-analysus-content {
      margin-top: 10px;
      // min-height: 500px;
    }
  }

  .asset-ai-chart-wrap {
    --chart-label-color: #000;
    // background: #fff;
  }

  .collapse-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1000;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
