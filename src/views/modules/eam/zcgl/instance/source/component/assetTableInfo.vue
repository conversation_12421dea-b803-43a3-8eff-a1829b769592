<template>
  <div class="instance">
    <div style="flex-wrap: nowrap">
      <splitpane :splitSet="settingLR">
        <template #paneL>
          <div style="padding-left: 5px" class="bottomLR">
            <el-tree-select
              placeholder="请选择所属组织"
              v-model="selectTreeValue"
              :data="tmpTreeItemData"
              check-strictly
              :render-after-expand="false"
              :props="defaultProps"
              node-key="id"
              node-value="id"
              filterable
              clearable
            />
            <el-input
              clearable
              v-model="filterText"
              style="
                width: 100%;
                height: 1.5rem;
                line-height: 2rem;
                margin-top: 0.5rem;
                margin-bottom: 0.5rem;
                font-size: 14px;
              "
              placeholder="请输入关键字"
              :suffix-icon="Search"
            />
            <!-- default-expand-all -->
            <div style="overflow: auto" v-loading="treeLoading">
              <el-tree
                ref="treeRef"
                style="max-height: 50rem"
                class="filter-tree"
                :data="tmpTreeTypeData"
                :props="defaultProps"
                node-key="id"
                label="title"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                highlight-current
                :current-node-key="currentNodeKey"
                :default-expanded-keys="defaultExpandedKeys"
                @node-click="nodeClick"
              >
                <template #default="{ node, data }">
                  <div
                    class="custom-tree-node"
                    style="
                      display: flex;
                      justify-content: space-between;
                      flex-grow: 1;
                      font-size: 13px;
                      line-height: 17px;
                    "
                  >
                    <div style="display: inline-block">{{ node.label }}</div>
                    <el-dropdown
                      placement="bottom"
                      @command="val => categoryCommand(val, node.data)"
                    >
                      <el-button
                        type="primary"
                        link
                        style="margin-right: 15px"
                        @click.stop="stopClick"
                        :icon="useRenderIcon('EP-MoreFilled')"
                      ></el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item
                            v-if="node.data.id != 0"
                            command="updateCategory"
                          >
                            <template #default>
                              <div style="width: 100%; text-align: center">
                                编辑
                              </div>
                            </template>
                          </el-dropdown-item>
                          <el-dropdown-item
                            command="primaryUpdate"
                            v-if="
                              !node.data.children ||
                              node.data.children.length == 0
                            "
                          >
                            <template #default>
                              <div style="width: 100%; text-align: center">
                                主键维护
                              </div>
                            </template>
                          </el-dropdown-item>
                          <el-dropdown-item
                            command="relationProperty"
                            v-if="
                              !node.data.children ||
                              node.data.children.length == 0
                            "
                          >
                            <template #default>
                              <div style="width: 100%; text-align: center">
                                关联属性
                              </div>
                            </template>
                          </el-dropdown-item>
                          <el-dropdown-item
                            command="infoCategory"
                            v-if="
                              !node.data.children ||
                              node.data.children.length == 0
                            "
                          >
                            <template #default>
                              <div style="width: 100%; text-align: center">
                                详情类别
                              </div>
                            </template>
                          </el-dropdown-item>
                          <el-dropdown-item
                            v-if="
                              !node.data.children ||
                              node.data.children.length == 0
                            "
                            command="groupSetting"
                          >
                            <template #default>
                              <div style="width: 100%; text-align: center">
                                分组设置
                              </div>
                            </template>
                          </el-dropdown-item>
                          <el-dropdown-item command="clickSetColumn">
                            <template #default>
                              <div style="width: 100%; text-align: center">
                                展示列配置
                              </div>
                            </template>
                          </el-dropdown-item>
                          <el-dropdown-item
                            v-if="
                              !node.data.children ||
                              node.data.children.length == 0
                            "
                            command="topoPropertySet"
                          >
                            <template #default>
                              <div style="width: 100%; text-align: center">
                                拓扑属性
                              </div>
                            </template>
                          </el-dropdown-item>
                          <el-dropdown-item
                            v-if="node.data.id != 0"
                            command="deleteCategory"
                          >
                            <template #default>
                              <div style="width: 100%; text-align: center">
                                删除
                              </div>
                            </template>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                    <!--                  <div style="display: inline-block; padding-right: 0.6rem">-->
                    <!--                    <a @click="assetClassificationOperationDialog = true" style="color: #409eff"> 修改 </a>-->
                    <!--                  </div>-->
                  </div>
                </template>
              </el-tree>
            </div>
          </div>
        </template>
        <template #paneR>
          <div style="height: 100%; overflow-y: auto">
            <div>
              <el-form
                :inline="true"
                :model="searchFormInline"
                class="instance-form-inline"
                style="padding-bottom: 1rem"
              >
                <el-row style="padding-bottom: 1rem">
                  <el-col :span="24">
                    <div>
                      <el-row>
                        <el-col :span="17">
                          <el-form
                            ref="queryCondition"
                            :model="queryConditionForm"
                            label-width="90px"
                            @submit.native.prevent
                          >
                            <div class="flex-c">
                              <el-input
                                v-model="queryConditionForm.allName"
                                :disabled="isShow"
                                placeholder="IP地址、资产名称"
                                class="w-1/2"
                              >
                                <template #append>
                                  <el-button
                                    v-if="!isShow"
                                    @click="clickShow"
                                    style="
                                      font-size: 12px;
                                      height: 1.6rem;
                                      line-height: 1.5rem;
                                      padding-top: 1px;
                                    "
                                    :icon="useRenderIcon('EP-CirclePlus')"
                                    >展开</el-button
                                  >
                                  <el-button
                                    v-else
                                    @click="clickShow"
                                    style="
                                      font-size: 12px;
                                      height: 1.6rem;
                                      line-height: 1.5rem;
                                      padding-top: 1px;
                                    "
                                    :icon="useRenderIcon('EP-Remove')"
                                    >收起</el-button
                                  >
                                </template>
                              </el-input>
                              <el-button
                                type="primary"
                                class="ml-3"
                                @click="queryFunction"
                                >查询</el-button
                              >
                              <el-button
                                type="primary"
                                class="ml-3"
                                @click="clickSetQuery"
                                >高级检索</el-button
                              >
                              <el-button
                                type="primary"
                                class="ml-3"
                                @click="resetFunction"
                                >重置</el-button
                              >
                            </div>
                          </el-form>
                        </el-col>
                        <el-col :span="6" style="padding-top: 2px">
                          <el-button
                            :disabled="isChildNodes"
                            @click="newAddAssets"
                            icon="el-icon-plus"
                            type="primary"
                            >新增资产
                          </el-button>
                          <el-button
                            :disabled="isChildNodes"
                            @click="importShow = true"
                            >导入</el-button
                          >

                          <el-dropdown
                            style="margin-left: 0.7rem"
                            placement="bottom"
                            @command="handleCommand"
                          >
                            <el-button style="margin-top: 2px">导出</el-button>
                            <template #dropdown>
                              <el-dropdown-menu>
                                <el-dropdown-item
                                  :disabled="isChildNodes"
                                  style="padding: 0.3rem 1rem"
                                  command="exportCate"
                                >
                                  按类别导出
                                </el-dropdown-item>
                                <el-dropdown-item
                                  style="padding: 0.3rem 1rem"
                                  command="exportAll"
                                >
                                  导出所有数据
                                </el-dropdown-item>
                                <el-dropdown-item
                                  style="padding: 0.3rem 1rem"
                                  command="exportSearch"
                                >
                                  按查询导出
                                </el-dropdown-item>
                                <el-dropdown-item
                                  style="padding: 0.3rem 1rem"
                                  command="exportSelect"
                                >
                                  按勾选导出
                                </el-dropdown-item>
                                <el-dropdown-item
                                  :disabled="isChildNodes"
                                  style="padding: 0.3rem 1rem"
                                  command="exportTemp"
                                >
                                  导出模板
                                </el-dropdown-item>
                                <el-dropdown-item
                                  style="padding: 0.3rem 1rem"
                                  command="exportRelation"
                                >
                                  资产关系导出
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </template>
                          </el-dropdown>
                          <div
                            class="dropdown-menu"
                            style="
                              display: inline-block;
                              margin-left: 0.7rem;
                              vertical-align: -0.1rem;
                            "
                          >
                            <el-dropdown
                              @command="handleMoreCommand"
                              placement="bottom"
                            >
                              <el-button>更多操作</el-button>
                              <template #dropdown>
                                <el-dropdown-menu>
                                  <el-dropdown-item
                                    :disabled="selectedTableData.length == 0"
                                    command="batchDeletion"
                                    style="padding: 0.3rem 1rem"
                                    >批量删除
                                  </el-dropdown-item>
                                  <el-dropdown-item
                                    :disabled="selectedTableData.length == 0"
                                    command="batchModification"
                                    style="padding: 0.3rem 1rem"
                                    >批量修改
                                  </el-dropdown-item>
                                  <el-dropdown-item
                                    command="showInstanceDelete"
                                    style="padding: 0.3rem 1rem"
                                    >资产数据回收
                                  </el-dropdown-item>
                                </el-dropdown-menu>
                              </template>
                            </el-dropdown>
                          </div>
                        </el-col>
                      </el-row>
                      <div
                        v-show="isShow"
                        style="
                          margin-top: 15px;
                          max-height: 210px;
                          overflow-y: auto;
                        "
                        class="expandDiv"
                      >
                        <el-form
                          ref="querySwithFormRef"
                          :model="querySwithForm"
                          label-width="120px"
                        >
                          <el-row>
                            <el-col :span="12" v-show="ip_mask_state == 'open'">
                              <el-form-item label="IP掩码">
                                <el-input
                                  v-model.trim="querySwithForm.ipaddr"
                                  placeholder="IP地址"
                                  style="width: 16rem"
                                ></el-input>
                                <span
                                  style="
                                    width: 1rem;
                                    display: inline-block;
                                    font-weight: bolder;
                                    text-align: center;
                                    color: black;
                                  "
                                  >/</span
                                >
                                <el-input
                                  v-model.trim="querySwithForm.yanma"
                                  placeholder="掩码"
                                  style="width: 6rem"
                                ></el-input>
                              </el-form-item>
                            </el-col>
                            <el-col :span="12" v-show="ip_seg_state == 'open'">
                              <el-form-item label="IP地址段">
                                <el-input
                                  v-model.trim="querySwithForm.startIp"
                                  placeholder="起始IP"
                                  style="width: 10rem"
                                ></el-input>
                                <span
                                  style="
                                    width: 3rem;
                                    display: inline-block;
                                    font-weight: bolder;
                                    text-align: center;
                                    color: black;
                                  "
                                  >---</span
                                >
                                <el-input
                                  v-model.trim="querySwithForm.endIp"
                                  placeholder="结束IP"
                                  style="width: 10rem"
                                ></el-input>
                              </el-form-item>
                            </el-col>
                          </el-row>
                        </el-form>
                        <el-form
                          ref="queryCondition"
                          :model="queryConditionForm"
                          label-width="120px"
                        >
                          <el-row
                            v-for="(col, index0) in propertyForm.rows"
                            :key="index0"
                          >
                            <el-col
                              :span="
                                item.field.showType == 'objectTable' ? 24 : 12
                              "
                              v-for="(item, index1) in col.colList"
                              :key="index1"
                            >
                              <!--可编辑，并且没有单位的属性-->
                              <span
                                v-if="item.field != null && item.rule != null"
                              >
                                <el-form-item
                                  :label="item.field.name"
                                  :key="index0"
                                >
                                  <span
                                    v-if="item.field.showType == 'objectTable'"
                                  >
                                    <query-object-table
                                      :ref="
                                        el => getObjRef(el, item.field.code)
                                      "
                                      :queryData.sync="item.field.value"
                                      :queryList="item.field.tableList"
                                      :ocode="item.field.ocode"
                                    ></query-object-table>
                                  </span>
                                  <!--普通输入框 input （string，number）-->
                                  <span
                                    v-if="
                                      item.field.showType == 'input' ||
                                      item.field.showType == 'telephone' ||
                                      item.field.showType == 'textarea' ||
                                      item.field.showType == 'email' ||
                                      item.field.showType == 'phone' ||
                                      item.field.showType == 'snmp_addr'
                                    "
                                  >
                                    <!--数据类型 input、number、text、select、checkbox、radio-->
                                    <span>
                                      <el-input
                                        v-model.trim="item.field.value"
                                        :placeholder="item.field.name"
                                        :precise.sync="item.field.hide"
                                      >
                                      </el-input>
                                      <!--<el-input v-model="item.field.value"-->
                                      <!--:placeholder="item.field.name"></el-input>-->
                                    </span>
                                  </span>
                                  <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
                                  <span
                                    v-if="
                                      item.field.showType == 'dateTime' ||
                                      item.field.showType == 'date'
                                    "
                                  >
                                    <el-date-picker
                                      clearable
                                      v-model="item.field.value"
                                      type="datetimerange"
                                      range-separator="到"
                                      value-format="YYYY-MM-DD HH:mm:ss"
                                      format="YYYY-MM-DD HH:mm:ss"
                                      start-placeholder="开始日期"
                                      end-placeholder="结束日期"
                                    />
                                  </span>
                                  <span v-if="item.field.showType == 'number'">
                                    <el-input-number
                                      v-model.trim="item.field.value"
                                    ></el-input-number>
                                  </span>
                                  <span
                                    v-if="item.field.showType == 'checkbox'"
                                  >
                                    <el-checkbox-group
                                      v-model="item.field.value"
                                    >
                                      <el-checkbox
                                        :label="cn.value"
                                        v-for="(cn, index) in item.field
                                          .enumArray"
                                        :key="index"
                                        >{{ cn.label }}
                                      </el-checkbox>
                                    </el-checkbox-group>
                                  </span>
                                  <span
                                    v-if="
                                      item.field.showType == 'windowboxTree'
                                    "
                                  >
                                    <el-input
                                      v-model.trim="item.field.showValue"
                                      readonly
                                      clearable
                                      :title="item.field.showValue"
                                      class="ellipsis"
                                      :placeholder="item.field.name"
                                    >
                                      <el-button
                                        slot="prepend"
                                        icon="el-icon-search"
                                        @click="openWindowTree(item)"
                                      ></el-button>
                                      <el-button
                                        slot="append"
                                        icon="el-icon-close"
                                        @click="clearWindowTree(item)"
                                      ></el-button>
                                    </el-input>
                                  </span>

                                  <span
                                    v-if="
                                      item.field.showType == 'windowboxFilter'
                                    "
                                  >
                                    <div
                                      style="
                                        overflow: hidden;
                                        display: inline-table;
                                        vertical-align: top;
                                        width: 100%;
                                      "
                                    >
                                      <div
                                        style="
                                          display: table-cell;
                                          width: 44px;
                                          vertical-align: top;
                                        "
                                      >
                                        <el-button
                                          icon="el-icon-search"
                                          @click="openFilterSelect(item)"
                                        ></el-button>
                                      </div>
                                      <div style="display: table-cell">
                                        <el-select
                                          style="width: 100%"
                                          v-model="item.field.value"
                                          popper-append-to-body
                                          multiple
                                          filterable
                                          remote
                                          reserve-keyword
                                          placeholder="请输入关键词"
                                          @focus="
                                            val => clearTableList(val, item)
                                          "
                                          :remote-method="
                                            vul => remoteMethod(vul, item)
                                          "
                                        >
                                          <el-option
                                            v-for="item1 in item.field
                                              .tableList"
                                            :key="item1.value"
                                            :label="item1.label"
                                            :value="item1.value"
                                          >
                                          </el-option>
                                        </el-select>
                                      </div>
                                      <div
                                        style="
                                          display: table-cell;
                                          width: 44px;
                                          vertical-align: top;
                                        "
                                      >
                                        <el-button
                                          icon="el-icon-close"
                                          @click="clearSelect(item)"
                                        ></el-button>
                                      </div>
                                    </div>
                                  </span>
                                  <span
                                    v-if="
                                      item.field.showType ==
                                      'windowboxTreeFilter'
                                    "
                                  >
                                    <div
                                      style="
                                        overflow: hidden;
                                        display: inline-table;
                                        vertical-align: top;
                                        width: 100%;
                                      "
                                    >
                                      <div
                                        style="
                                          display: table-cell;
                                          width: 44px;
                                          vertical-align: top;
                                        "
                                      >
                                        <el-button
                                          icon="el-icon-search"
                                          @click="openWindowTree(item)"
                                        ></el-button>
                                      </div>
                                      <div style="display: table-cell">
                                        <select-popover
                                          v-model="item.field.value"
                                          :options="item.field.tableList"
                                          @focus="
                                            val => clearTreeTableList(val, item)
                                          "
                                          @remote-method="
                                            vul => remoteTreeMethod(vul, item)
                                          "
                                        ></select-popover>
                                      </div>
                                      <div
                                        style="
                                          display: table-cell;
                                          width: 44px;
                                          vertical-align: top;
                                        "
                                      >
                                        <el-button
                                          icon="el-icon-close"
                                          @click="clearWindowTree(item)"
                                        ></el-button>
                                      </div>
                                    </div>
                                  </span>
                                  <span
                                    v-if="item.field.showType == 'inputSelect'"
                                  >
                                    <el-input
                                      v-model="item.field.value"
                                      :placeholder="item.field.name"
                                      :precise.sync="item.field.hide"
                                    >
                                    </el-input>
                                  </span>
                                  <span
                                    v-if="item.field.showType == 'moreInput'"
                                  >
                                    <el-input
                                      v-model="item.field.value"
                                      :placeholder="item.field.name"
                                      :precise.sync="item.field.hide"
                                    >
                                    </el-input>
                                  </span>
                                  <!--单选-->
                                  <span v-if="item.field.showType == 'radio'">
                                    <el-radio-group v-model="item.field.value">
                                      <template
                                        v-for="(cn, index) in item.field
                                          .enumArray"
                                      >
                                        <el-radio :value="cn.value">{{
                                          cn.label
                                        }}</el-radio>
                                      </template>
                                    </el-radio-group>
                                  </span>
                                  <span
                                    v-if="item.field.showType == 'cascader'"
                                  >
                                    <el-cascader
                                      :options="item.field.enumArray"
                                      v-model="item.field.value"
                                    ></el-cascader>
                                  </span>
                                  <span
                                    v-if="item.field.showType == 'windowbox'"
                                  >
                                    <el-input
                                      v-model.trim="item.field.value"
                                      icon="ios-search"
                                      :placeholder="item.field.name"
                                    />
                                  </span>
                                  <span
                                    v-if="
                                      item.field.showType == 'mul_windowbox'
                                    "
                                  >
                                    <el-input
                                      v-model.trim="item.field.value"
                                      icon="ios-search"
                                      :placeholder="item.field.name"
                                    />
                                  </span>
                                  <!-- 复选下拉-->

                                  <span
                                    v-if="item.field.showType == 'mul_combobox'"
                                  >
                                    <el-select
                                      placement="top"
                                      filterable
                                      :multiple="true"
                                      v-model="item.field.value"
                                    >
                                      <el-option
                                        v-for="(en, index) in item.field
                                          .enumArray"
                                        :value="en.value"
                                        :label="en.label"
                                        :key="index"
                                      ></el-option>
                                    </el-select>
                                  </span>
                                  <span
                                    v-if="item.field.showType == 'comboTree'"
                                  >
                                    <el-tree-select
                                      v-model="item.field.value"
                                      node-value="id"
                                      node-key="id"
                                      :data="item.field.enumArray"
                                      filterable
                                      clearable
                                      :props="defaultProps"
                                    ></el-tree-select>
                                  </span>
                                  <span
                                    v-if="
                                      item.field.isCascade == 1 &&
                                      item.field.activeProp != ''
                                    "
                                  >
                                    <!--主动级联，且被别的属性关联-->
                                    <!-- 下拉选 -->
                                    <span
                                      v-if="item.field.showType == 'combobox'"
                                    >
                                      <el-select
                                        placement="top"
                                        filterable
                                        :multiple="true"
                                        :clearable="true"
                                        style="width: 100%"
                                        v-model="item.field.value"
                                      >
                                        <el-option
                                          v-for="(en, index) in item.field
                                            .enumArray"
                                          :key="index"
                                          :value="en.value"
                                          :label="en.label"
                                        >
                                        </el-option>
                                      </el-select>
                                    </span>
                                  </span>

                                  <span
                                    v-if="
                                      item.field.isCascade == 0 &&
                                      item.field.activeProp != ''
                                    "
                                  >
                                    <!--不是主动级联,但是被级联的-->
                                    <!-- 下拉选 -->
                                    <span
                                      v-if="item.field.showType == 'combobox'"
                                    >
                                      <el-select
                                        placement="top"
                                        filterable
                                        :multiple="true"
                                        style="width: 100%"
                                        v-model="item.field.value"
                                        clearable
                                      >
                                        <el-option
                                          v-for="(en, index) in item.field
                                            .enumArray"
                                          :key="index"
                                          :value="en.value"
                                          :label="en.label"
                                        ></el-option>
                                      </el-select>
                                    </span>
                                  </span>

                                  <span
                                    v-if="
                                      item.field.isCascade == 0 &&
                                      item.field.activeProp == ''
                                    "
                                  >
                                    <span
                                      v-if="item.field.showType == 'combobox'"
                                    >
                                      <el-select
                                        placement="top"
                                        filterable
                                        :multiple="true"
                                        :clearable="true"
                                        style="width: 100%"
                                        v-model="item.field.value"
                                      >
                                        <el-option
                                          v-for="(en, index) in item.field
                                            .enumArray"
                                          :value="en.value"
                                          :key="index"
                                          :label="en.label"
                                        >
                                        </el-option>
                                      </el-select>
                                    </span>
                                  </span>
                                </el-form-item>
                              </span>
                            </el-col>
                          </el-row>
                        </el-form>
                      </div>
                    </div>
                  </el-col>
                  <!-- 多功能区域 -->
                </el-row>
              </el-form>
            </div>
            <el-row style="padding-bottom: 1rem" class="centerCard">
              <span
                style="
                  width: 6em;
                  font-size: 0.9em;
                  line-height: 1.9em;
                  margin-left: 0.6rem;
                "
                >资产标签：</span
              >
              <!-- {{ selectedCard + '' }} -->
              <el-check-tag
                :class="{ selected: selectedCard == 'all' || !selectedCard }"
                style="
                  cursor: pointer;
                  display: flex;
                  width: auto;
                  justify-content: center;
                  margin-right: 0.6rem;
                  color: #fff;
                "
                @click="selectCard({ value: 'all' })"
                ><span>全部</span></el-check-tag
              >
              <span v-for="(card, index) in cards" :key="index">
                <el-check-tag
                  :class="{ selected: selectedCard == card.value }"
                  style="
                    cursor: pointer;
                    display: flex;
                    width: auto;
                    justify-content: center;
                    margin-right: 0.6rem;
                  "
                  @click="selectCard(card)"
                >
                  <span style="white-space: nowrap">{{ card.label }}</span>
                  <span> ({{ card.count }}) </span>
                </el-check-tag>
              </span>
            </el-row>
            <avue-crud
              :data="dataTable"
              :option="tableOption"
              v-model:page="state.pagination"
              @row-dblclick="showInstance"
              @size-change="handlePageSizeChange"
              :table-loading="tableLoading"
              @selection-change="chagenSelection"
              @current-change="handlePageCurrentChange"
            >
              <template
                v-for="(item, index) in tableOption.column"
                #[item.headerSlot]="{ column }"
              >
                <span v-if="column.prop == 'asset_label_field'"
                  >{{ column.label }}{{ propv[column.prop] }}</span
                >
                <span style="position: relative; display: inline-block" v-else>
                  <span style="position: relative; display: inline-block">{{
                    column.label
                  }}</span>
                  <span style="position: absolute; width: 20px">&nbsp;</span>

                  <el-popover
                    placement="bottom"
                    width="320"
                    :ref="el => getPropRef(el, column.prop)"
                    @hide="closePopover(column)"
                    :visible="propv[column.prop]"
                    append-to-body
                    trigger="click"
                  >
                    <template #reference>
                      <el-button
                        type="primary"
                        link
                        :icon="useRenderIcon('EP-Filter')"
                        v-if="
                          colorMap[column.prop] &&
                          colorMap[column.prop].length > 0
                        "
                        style="
                          position: absolute;
                          top: 3px;
                          margin-left: 2px;
                          color: #409eff;
                        "
                        @click="
                          loadColumnFilter(
                            'filterSearch-' +
                              page.currentNodeId +
                              '-' +
                              column.prop,
                            column
                          )
                        "
                      ></el-button>
                      <el-button
                        type="primary"
                        link
                        :icon="useRenderIcon('EP-Filter')"
                        v-else
                        style="
                          position: absolute;
                          top: 3px;
                          margin-left: 2px;
                          color: #8d8d8d;
                        "
                        @click="
                          loadColumnFilter(
                            'filterSearch-' +
                              page.currentNodeId +
                              '-' +
                              column.prop,
                            column
                          )
                        "
                      ></el-button>
                    </template>
                    <!--                                            <template #default>-->
                    <!--                                            </template>-->
                    <filter-column-search-table
                      v-model:visiable="propv[column.prop]"
                      v-if="propv[column.prop]"
                      :ref="el => getFilterRef(el, column.prop)"
                      v-model:color-map="state.colorMap"
                      :isShow="isShow"
                      :queryCondition="queryCondition"
                      :querySwithForm="querySwithForm"
                      :propertyForm="propertyForm"
                      :organizationSelectValue="page.deptZoneId"
                      v-click-outside="onClickOutside"
                      @queryInstanceTable="initTableData"
                      :categoryId="page.currentNodeId"
                      @changeSort="changeFilterSort"
                      :column="column"
                      @closePopover="closePopover(column)"
                    >
                    </filter-column-search-table>
                  </el-popover>
                </span>
              </template>

              <template #asset_label_field="{ row, $index }">
                <template
                  v-if="
                    row['asset_label_field'] &&
                    row['asset_label_field'].length > 0
                  "
                >
                  <!-- <el-tag v-for="item in row['asset_label_field']" type="primary">{{ item }}</el-tag> -->
                  <el-tag
                    v-if="row['asset_label_field'].indexOf('僵尸资产') >= 0"
                    style="color: white; background-color: #1890ff"
                    >僵尸资产</el-tag
                  >
                  <el-tag
                    v-if="row['asset_label_field'].indexOf('事件资产') >= 0"
                    style="color: white; background-color: #f5222d"
                    >事件资产</el-tag
                  >
                  <el-tag
                    v-if="row['asset_label_field'].indexOf('漏洞资产') >= 0"
                    style="color: white; background-color: #f76267"
                    >漏洞资产</el-tag
                  >
                  <el-tag
                    v-if="row['asset_label_field'].indexOf('已封堵') >= 0"
                    style="color: white; background-color: #f59a23"
                    >已封堵</el-tag
                  >
                  <el-tag
                    v-if="row['asset_label_field'].indexOf('属性缺失') >= 0"
                    style="color: white; background-color: #73d13d"
                    >属性缺失</el-tag
                  >
                  <el-tag
                    v-if="row['asset_label_field'].indexOf('属性重复') >= 0"
                    style="color: white; background-color: #73d13d"
                    >属性重复</el-tag
                  >
                </template>
              </template>
              <template #onlineStatus__label="{ row, $index }">
                <div v-if="row['onlineStatus__label'] == '离线'">
                  <div
                    style="
                      display: inline-block;
                      width: 12px;
                      height: 12px;
                      border: 1px solid grey;
                      background-color: grey;
                      border-radius: 6px;
                      margin-right: 5px;
                      overflow: hidden;
                    "
                  >
                    <el-icon
                      style="color: white; font-size: 8px; vertical-align: 7px"
                      ><CloseBold
                    /></el-icon>
                  </div>
                  <span style="vertical-align: 2px">离线</span>
                </div>
                <div v-if="row['onlineStatus__label'] == '在线'">
                  <div
                    style="
                      display: inline-block;
                      width: 12px;
                      height: 12px;
                      border: 1px solid green;
                      background-color: green;
                      border-radius: 6px;
                      margin-right: 5px;
                      overflow: hidden;
                    "
                  >
                    <el-icon
                      style="color: white; font-size: 8px; vertical-align: 6px"
                      ><Select
                    /></el-icon>
                  </div>
                  <span style="vertical-align: 2px">在线</span>
                </div>
              </template>

              <template #operator="{ row, size, $index }">
                <!-- ai研判 -->
                <el-button
                  :size="size"
                  type="primary"
                  text
                  v-if="enableAI"
                  :disabled="!row.ipAddress"
                  @click="openAssetAiDrawer(row)"
                >
                  <template #icon>
                    <img :src="aiPng" width="16" height="16" />
                  </template>
                  智能评估
                </el-button>

                <el-button type="primary" link @click="updateInstance(row)"
                  >编辑</el-button
                >
                <el-button
                  type="primary"
                  link
                  @click="deleteInstanceByRow(row)"
                  style="margin-left: 4px"
                  >删除</el-button
                >
                <el-dropdown
                  placement="bottom"
                  @command="val => clickCommand(val, row)"
                  style="vertical-align: center"
                >
                  <el-button type="primary" link style="padding-top: 6px"
                    >更多</el-button
                  >
                  <template #dropdown>
                    <el-dropdown-menu>
                      <!--                      <el-dropdown-item style="padding: 0.3rem 1rem;" command="updateInstance">-->
                      <!--                        编辑-->
                      <!--                      </el-dropdown-item>-->
                      <!--                      <el-dropdown-item style="padding: 0.3rem 1rem;" command="handleLeftDeleteClick">-->
                      <!--                        删除-->
                      <!--                      </el-dropdown-item>-->
                      <el-dropdown-item
                        style="padding: 0.3rem 1rem"
                        command="onlienCollection"
                      >
                        在线探测
                      </el-dropdown-item>
                      <el-dropdown-item
                        style="padding: 0.3rem 1rem"
                        command="showInstance"
                      >
                        查看详情
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </avue-crud>
          </div>
        </template>
      </splitpane>
    </div>
    <!-- 导入数据抽屉 -->
    <el-drawer
      size="50%"
      destroy-on-close
      v-model="importShow"
      @close="closeAssetImport"
    >
      <template #header="{ close, titleId, titleClass }">
        <h4 :id="titleId" :class="titleClass">导入</h4>
      </template>
      <assetImport
        :categoryId="page.currentNodeId"
        :categoryName="''"
        :visible.sync="importShow"
        @closeImport="closeAssetImport"
      />
    </el-drawer>
    <!-- <assetImport :categoryId="page.currentNodeId" :categoryName="'selectCateName'" :visible.sync="importShow"
      @closeImport="closeAssetImport" /> -->

    <!-- 更多操作弹窗引入 -->
    <el-dialog
      destroy-on-close
      v-model="moreOperationsDialog"
      :title="`属性分组配置(${selectCateName})`"
      width="100vw"
      style="height: 100vh; margin: 0; box-sizing: border-box"
    >
      <instanceShowGroup
        @closeSetShowGroup="closeSetShowGroup"
        :categoryId="selectCate"
        :categoryName="selectCateName"
      />
    </el-dialog>

    <!-- 批量编辑弹窗 -->
    <el-dialog
      destroy-on-close
      v-model="more_update_property"
      v-if="more_update_property"
      title="批量修改"
    >
      <moreUpdateProperty
        @closeMoreUpdate="() => (more_update_property = false)"
        @refreshTable="initTable"
        :ids="selectedTableData"
      />
    </el-dialog>
    <el-dialog
      v-model="windowTreeDialog"
      :modal="false"
      fullscreen
      append-to-body
      destroy-on-close
      style="z-index: 99999"
    >
      <windowBoxTree
        :item="treeItem"
        v-if="windowTreeDialog"
        @closeWindowTree="closeWindowTree"
      ></windowBoxTree>
    </el-dialog>
    <el-dialog
      destroy-on-close
      v-model="state.editCategoryShow"
      append-to-body
      width="700px"
      title="类别维护"
      @close="state.editCategoryShow = false"
      style="padding-left: 50px; padding-right: 50px"
    >
      <div style="margin-top: 10px">
        <el-form
          :model="categoryData"
          :rules="rulesInfo"
          ref="categoryDataForm"
          label-width="120px"
          class="demo-ruleForm"
        >
          <el-row>
            <el-form-item
              label="父类别名称："
              prop="parentName"
              style="width: 100%"
            >
              <el-input v-model="categoryData.parentName" :disabled="true">
              </el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="类别名称：" prop="name" style="width: 100%">
              <el-input v-model="categoryData.name"> </el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="描述：" prop="description" style="width: 100%">
              <el-input
                type="textarea"
                placeholder="描述"
                style="width: 100%"
                v-model="categoryData.description"
                :disabled="false"
              >
              </el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item
              label="类别状态："
              prop="description"
              style="width: 100%"
            >
              <el-radio-group v-model="categoryData.state">
                <el-radio value="enable">启用</el-radio>
                <el-radio value="disable">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item
              label="拓扑类型："
              prop="topoType"
              style="width: 100%"
            >
              <el-select v-model="categoryData.topoType">
                <el-option
                  v-for="item in topoList"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item
              label="类别展示："
              prop="categoryType"
              style="width: 100%"
            >
              <el-select v-model="categoryData.categoryType">
                <el-option label="资产台账" :value="'1'" key="1"></el-option>
                <el-option label="资产详情" :value="'2'" key="2"></el-option>
                <el-option label="全部" :value="'3'" key="3"></el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row
            v-if="
              categoryData.categoryType == '1' ||
              categoryData.categoryType == '3'
            "
          >
            <el-form-item
              label="是否展示详情类别："
              prop="showType"
              style="width: 100%"
            >
              <el-select v-model="categoryData.showType">
                <el-option label="是" :value="'1'" key="1"></el-option>
                <el-option label="否" :value="'2'" key="2"></el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="state.editCategoryShow = false">关闭</el-button>
          <el-button type="primary" @click="saveCategory"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-if="relationPropertyShow"
      v-model="relationPropertyShow"
      width="1200px;"
      title="关联属性维护"
    >
      <category-relation-property
        v-if="relationPropertyShow"
        :categoryData="state.relationCategory"
      ></category-relation-property>
    </el-dialog>

    <el-dialog
      v-if="infoCategoryShow"
      v-model="infoCategoryShow"
      width="700px;"
      title="详情类别维护"
    >
      <avue-crud
        :data="infoCategoryData"
        :option="infoCategoryOption"
        @sortable-change="changeSort"
      >
        <template #checked="{ row }">
          <el-switch
            v-model="row.checked"
            class="ml-2"
            inline-prompt
            style="
              --el-switch-on-color: #13ce66;
              --el-switch-off-color: #ff4949;
            "
            active-text="显示"
            inactive-text="不显示"
            :active-value="'0'"
            :inactive-value="'1'"
          />
        </template>
      </avue-crud>
      <template #footer>
        <el-button @click="state.infoCategoryShow = false">关闭</el-button>
        <el-button type="primary" @click="saveInfoCategoryRelation">
          保存
        </el-button>
      </template>
    </el-dialog>

    <el-dialog
      title="查询条件维护"
      v-if="conditionShow"
      v-model="conditionShow"
      :show-close="false"
      fullscreen
      @close="cancalCondition"
      style="width: 80%; margin: 0 auto"
    >
      <template #header>
        <div style="position: relative">
          <span>查询条件维护</span>
          <div
            style="
              position: absolute;
              top: 5px;
              right: 5px;
              padding: 0;
              margin: 0;
            "
          >
            <el-button type="primary" @click="saveCondition">保存 </el-button>
            <el-button type="info" @click="cancalConditionDialog"
              >取消
            </el-button>
          </div>
        </div>
      </template>
      <el-tabs v-model="conditionName">
        <el-tab-pane label="查询条件配置" name="querySet">
          <instance-condition
            ref="instanceConditionRef"
            v-if="conditionName == 'querySet'"
            :categoryId="page.currentNodeId"
            @cancalCondition="cancalCondition"
          ></instance-condition>
        </el-tab-pane>
        <!--        <el-tab-pane label="查询条件模板配置" name="queryTemplateSet">-->
        <!--          <instance-condition-template-->
        <!--            v-if="conditionName == 'queryTemplateSet'"-->
        <!--            :categoryId="page.currentNodeId"-->
        <!--            @cancalCondition="cancalCondition"-->
        <!--          ></instance-condition-template>-->
        <!--        </el-tab-pane>-->
      </el-tabs>
    </el-dialog>

    <el-dialog
      title="展示设置"
      v-if="setShowColumn"
      v-model="setShowColumn"
      fullscreen
      :show-close="false"
      @close="closeShowColumn"
      append-to-body
    >
      <template #header>
        <span>展示设置({{ selectCateName }})</span>
        <div style="position: absolute; top: 20px; right: 20px">
          <el-button type="primary" @click="saveTable">保存</el-button>
          <el-button type="info" @click="closeDialog">取消</el-button>
        </div>
      </template>
      <el-tabs v-model="tabName" v-if="isParentCategory">
        <el-tab-pane label="列表展示配置" name="tableTabSet">
          <instance-show-column
            ref="instanceShowColumnRef"
            :categoryId="selectCate"
            v-if="tabName == 'tableTabSet'"
            @closeShowColumn="closeShowColumn"
            @tableShowClick="tableShowClick"
            @initTableColumns="initTableColums"
          ></instance-show-column>
        </el-tab-pane>
        <!--        <el-tab-pane label="列表展示模板配置" name="tableTamplateSet">-->
        <!--          <instance-show-column-template :categoryId="page.currentNodeId" v-if="tabName=='tableTamplateSet'"-->
        <!--                                         @closeShowColumn="closeShowColumn"-->
        <!--                                         @tableShowClick="tableShowClick"-->
        <!--                                         @initTableColumns="initTableColums"></instance-show-column-template>-->
        <!--        </el-tab-pane>-->
        <el-tab-pane label="详情展示配置" name="detailTabSet">
          <detail-show-set
            ref="detailShowSetRef"
            :categoryId="selectCate"
            v-if="tabName == 'detailTabSet'"
            @closeShowColumn="closeShowColumn"
          >
          </detail-show-set>
        </el-tab-pane>
      </el-tabs>
      <el-tabs v-else v-model="tabName">
        <el-tab-pane label="列表展示配置" name="tableTabSet">
          <instance-show-column
            ref="instanceShowColumnRef"
            :categoryId="selectCate"
            v-if="tabName == 'tableTabSet'"
            @closeShowColumn="closeShowColumn"
            @tableShowClick="tableShowClick"
            @initTableColumns="initTableColums"
          ></instance-show-column>
        </el-tab-pane>
        <!--        <el-tab-pane label="列表展示模板配置" name="tableTamplateSet">-->
        <!--          <instance-show-column-template :categoryId="categoryId" v-if="tabName=='tableTamplateSet'"-->
        <!--                                         @closeShowColumn="closeShowColumn"-->
        <!--                                         @tableShowClick="tableShowClick"-->
        <!--                                         @initTableColumns="initTableColums"></instance-show-column-template>-->
        <!--        </el-tab-pane>-->
      </el-tabs>
    </el-dialog>

    <el-dialog
      title="主键维护"
      v-if="primaryEditShow"
      v-model="primaryEditShow"
      append-to-body
      width="500"
    >
      <el-form :model="primaryForm" ref="primaryFormRef">
        <el-form-item label="是否启用主键" prop="haveBool">
          <el-switch
            v-model="primaryForm.haveBool"
            class="ml-2"
            inline-prompt
            style="
              --el-switch-on-color: #13ce66;
              --el-switch-off-color: #ff4949;
            "
            active-text="启用"
            inactive-text="禁用"
            :active-value="'0'"
            inactive-value="'1'"
          />
        </el-form-item>
        <el-form-item
          label="主键"
          prop="primaryKey"
          v-if="primaryForm.haveBool == '0'"
          :rules="[
            { required: true, trigger: 'change', message: '请选择主键' }
          ]"
        >
          <el-select
            v-model="primaryForm.primaryKey"
            filterable
            multiple
            clearable
          >
            <el-option
              v-for="(item, index) in cateProList"
              :label="item.label"
              :value="item.value"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="state.primaryEditShow = false">关闭</el-button>
          <el-button type="primary" @click="savePrimary"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      :title="'拓扑属性维护(' + selectCateName + ')'"
      v-if="topoPropertySetShow"
      v-model="topoPropertySetShow"
      append-to-body
      width="600"
    >
      <el-row>
        <el-form
          ref="assetPositionFormRef"
          :model="assetPositionForm"
          label-width="120px"
        >
          <el-row>
            <el-form-item prop="positionImage" label="资产定位图标">
              <el-select
                v-model="assetPositionForm.positionImage"
                style="width: 240px"
                clearable
              >
                <template #label>
                  <img
                    :src="getImageUrl(assetPositionForm.positionImage)"
                    width="30"
                    height="30"
                  />
                </template>
                <el-option
                  v-for="(item, index) in imageList"
                  :label="item.label"
                  :value="item.value"
                  :key="index"
                >
                  <template #default>
                    <img
                      :src="getImageUrl(item.value)"
                      width="30"
                      height="30"
                    />
                  </template>
                </el-option>
              </el-select>
              <!--              <asset-image-select-->
              <!--                v-model="assetPositionForm.positionImage"-->
              <!--              ></asset-image-select>-->
              <!--<i v-for="item in iconList" style="font-size:50px;cursor:pointer;" :class="item" class="iconClass" @click="selectIconByPosition(item)"></i>-->
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item prop="topoImage" label="资产关系拓扑图标">
              <el-select
                v-model="assetPositionForm.topoImage"
                style="width: 240px"
                clearable
              >
                <template #label>
                  <img
                    :src="getImageUrl(assetPositionForm.topoImage)"
                    width="30"
                    height="30"
                  />
                </template>
                <el-option
                  v-for="(item, index) in imageList"
                  :label="item.label"
                  :value="item.topoImage"
                  :key="index"
                >
                  <template #default>
                    <img
                      :src="getImageUrl(item.topoImage)"
                      width="30"
                      height="30"
                    />
                  </template>
                </el-option>
              </el-select>
              <!--              <asset-image-select-->
              <!--                v-model="assetPositionForm.topoImage"-->
              <!--              ></asset-image-select>-->
            </el-form-item>
          </el-row>
          <el-row class="edit">
            <el-transfer
              ref="transferRef"
              v-model="assetPositionForm.checkProperty"
              filterable
              :data="tableData"
              :target-order="'push'"
              :titles="['未选择的属性', '已选择的属性']"
              :props="{ key: 'value', label: 'label' }"
              @change="transferChange"
            >
              <template #default="{ option }">
                <div
                  class="transferLabel"
                  :draggable="
                    assetPositionForm.checkProperty.includes(option.value)
                  "
                  @dragstart="handleDragStart(option)"
                  @dragenter="handleDragenter($event, option)"
                  @dragend="handleDrop()"
                >
                  <span class="transferValue">{{ option.label }}</span>
                  <span id="draggable" class="sort"></span>
                  <el-icon><Rank /></el-icon>
                </div>
              </template>
            </el-transfer>
          </el-row>
        </el-form>
      </el-row>
      <template #footer>
        <el-button type="primary" @click="state.topoPropertySetShow = false"
          >关闭</el-button
        >
        <el-button type="primary" @click="saveTopoProperty">保存</el-button>
      </template>
    </el-dialog>

    <!-- ai 智能研判 -->
    <AIAssetAnalysisDrawer
      v-model:visible="aiContext.drawerVisible"
      :asset-info="aiContext.asset"
    ></AIAssetAnalysisDrawer>
  </div>
</template>

<script lang="ts" setup>
import {
  getImages,
  getImageUrl
} from "@/views/modules/eam/zcgl/instance/source/api/topoAsset";
import {
  onMounted,
  reactive,
  ref,
  toRefs,
  watch,
  nextTick,
  computed
} from "vue";
import { Search, Rank } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElTree } from "element-plus";
import { ClickOutside as vClickOutside } from "element-plus";
import {
  initQueryLabelMethod,
  initSelectTreeDataMethod,
  initTableColumsMethod,
  initTableDataMethod,
  initTreeDataNewMethod,
  initConditionMethod,
  deleteInstanceByIds,
  queryLabelCountAxios,
  initAssetPositionSetAxios,
  saveAssetPositionSetAxios
} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import {
  deleteCategoryByIdAxios,
  categoryVerifyDelAxios,
  getCategoryByIdAxios,
  categoryVerifyNameAxios,
  saveCategoryAxios,
  queryCategoryPrimary,
  saveCategoryPrimary,
  queryPropertyByCategory,
  queryInfoRelationCategorySetting,
  saveInfoRelationCategorySetting,
  queryPropertyByIdAxios
} from "@/views/modules/eam/zcgl/instance/source/api/categoryModelInterface";
import { validataInstanceOnlineAxios } from "@/views/modules/eam/zcgl/instance/source/api/batchModificationInterface";

import {
  exportInstance,
  exportInsTemp,
  exportRelationReportAxios
} from "@/views/modules/eam/zcgl/instance/source/api/instanceAssetExport";
import { validateMoreUpdateAxios } from "@/views/modules/eam/zcgl/instance/source/api/batchModificationInterface";
import windowBoxTree from "@/views/modules/eam/zcgl/instance/source/component/windowBoxTree.vue";
import "element-plus/dist/index.css";
import assetImport from "@/views/modules/eam/zcgl/instance/source/component/assetImport.vue";
import instanceShowGroup from "@/views/modules/eam/zcgl/instance/source/component/instanceShowGroup.vue";
import moreUpdateProperty from "@/views/modules/eam/zcgl/instance/source/component/moreUpdate/moreUpdateProperty.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import QueryObjectTable from "@/views/modules/eam/zcgl/instance/source/component/queryObjectTable.vue";
import { queryEnumList } from "@/views/modules/eam/zcgl/instance/source/api/propertyModelInterface";
import SelectPopover from "@/views/modules/eam/zcgl/instance/source/component/selectPopover.vue";
import categoryRelationProperty from "@/views/modules/eam/zcgl/instance/source/component/categoryRelationProperty.vue";
import instanceCondition from "@/views/modules/eam/zcgl/instance/source/component/instanceCondition.vue";
import instanceShowColumn from "@/views/modules/eam/zcgl/instance/source/component/instanceShowColumn.vue";
import detailShowSet from "@/views/modules/eam/zcgl/instance/source/component/detailShowSet.vue";
import filterColumnSearch from "@/views/modules/eam/zcgl/instance/source/component/filterPage/filterColumnSearch.vue";
import filterColumnSearchTable from "@/views/modules/eam/zcgl/instance/source/component/filterPage/filterColumnSearchTable.vue";

import { useRoute } from "vue-router";
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { Select, CloseBold } from "@element-plus/icons-vue";
import aiPng from "@/views/modules/ai/assets/ai.png";
import { getConfig } from "@/config";
import AIAssetAnalysisDrawer from "./AIAssetAnalysisDrawer.vue";
const enableAI = computed(() => {
  return getConfig()?.EnableAI;
});
const aiContext = reactive({
  asset: null,
  drawerVisible: false
});
const openAssetAiDrawer = asset => {
  aiContext.asset = asset;
  aiContext.drawerVisible = true;
};

const settingLR: ContextProps = reactive({
  minPercent: 12,
  defaultPercent: 16,
  split: "vertical"
});
const route = useRoute();
// 这里定义接收的参数信息
const queryParams = reactive({
  label: "",
  zoneName: ""
});
const showFilter = ref(false);
watch(
  () => route?.query,
  val => {
    let { label, zoneName } = val || {};
    // 标签
    queryParams.label = label as string;
    queryParams.zoneName = zoneName as string;
    //
  },
  { immediate: true, deep: true }
);

const transferRef = ref();
const transferChange = (_, direction, moveKeys) => {
  if (direction === "right") {
    const arrList = state.tableData.filter(
      item => !moveKeys.includes(item.value)
    );
    const arrUnShift = state.tableData.filter(item =>
      moveKeys.includes(item.value)
    );
    state.tableData = [...arrList, ...arrUnShift];
  }
};
let dragTarget = ref(null);
let dragIndex = ref(1);
let targetOption = ref(null);
const handleDragStart = option => {
  dragTarget.value = option;
  dragIndex.value = state.assetPositionForm.checkProperty.findIndex(
    item => item === option.value
  );
};

const handleDragenter = (event, option) => {
  event.preventDefault();
  if (!dragTarget.value || !option) return;
  targetOption.value = option;
  if (event.target.draggable) {
    clearMovingDOM();
    const targetIndex = state.assetPositionForm.checkProperty.findIndex(
      item => item === targetOption.value.value
    );
    if (targetIndex < dragIndex.value) {
      event.target.className = "movingTop";
    } else {
      event.target.className = "movingBottom";
    }
  }
};
const handleDrop = () => {
  console.log("进入drop");
  console.log(targetOption.value);
  const targetIndex = state.assetPositionForm.checkProperty.findIndex(
    item => item === targetOption.value.value
  );
  const newIndex = targetIndex;
  const [removed] = state.assetPositionForm.checkProperty.splice(
    dragIndex.value,
    1
  );
  state.assetPositionForm.checkProperty.splice(newIndex, 0, removed);
  dragTarget.value = null;
  targetOption.value = null;
  dragIndex.value = -1;
  clearMovingDOM();
};
const clearMovingDOM = () => {
  console.log("进入clearMoving");
  document.querySelectorAll(".movingBottom").forEach(Element => {
    Element.className = "transferLabel";
  });
  document.querySelectorAll(".movingTop").forEach(Element => {
    Element.className = "transferLabel";
  });
};
const rulesInfo = ref({
  name: [
    {
      required: true,
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (value == "undefined" || value == null || value == "") {
          callback(new Error("请填写类别名称!!"));
        } else {
          categoryVerifyNameAxios(state.categoryData).then(res => {
            if (res.data.status != "success") {
              callback(new Error("类别名称已存在!!"));
            } else {
              callback();
            }
          });
        }
      }
    }
  ]
});

const filterRefs = ref(new Map());
const getFilterRef = (el: any, prop: String) => {
  filterRefs.value.set(
    "filterSearch-" + state.page.currentNodeId
      ? state.page.currentNodeId
      : "0" + "-" + prop,
    el
  );
};
const getPropRef = (el: any, prop: String) => {
  propRefs.value[
    "popover-" + state.page.currentNodeId
      ? state.page.currentNodeId
      : "0" + "-" + prop
  ] = el;
};
const categoryDataForm = ref();
const saveCategory = () => {
  categoryDataForm.value.validate(valid => {
    if (valid) {
      saveCategoryAxios(state.categoryData).then(res => {
        state.categoryData = res.data;
        ElMessage.success("保存成功");
        state.editCategoryShow = false;
        requestTreeDataNewMethod();
      });
    } else {
      return false;
    }
  });
};
const props = defineProps({
  sourceInfo: {
    type: String
  }
});
const deleteInstanceByRow = row => {
  let ids = [];
  ids.push(row._id);
  deleteInstance(ids);
};
const clickCommand = (command, row) => {
  if (command == "updateInstance") {
    updateInstance(row);
  } else if (command == "handleLeftDeleteClick") {
    deleteInstanceByRow(row);
  } else if (command == "onlienCollection") {
    onlienCollection(row);
  } else if (command == "showInstance") {
    showInstance(row);
  }
};
const treeUrl = [];
const tu = ref("");
const queryTreeUrl = async categoryId => {
  let node = treeRef.value.getNode(categoryId);
  treeUrl.push(node.data.name);
  await queryParent(node);
  let trees = [];
  for (let i = treeUrl.length - 1; i >= 0; i--) {
    trees.push(treeUrl[i]);
  }
  return trees.join("/");
};
const queryParent = async node => {
  if (node.data.parentId) {
    let node1 = node.parent;
    treeUrl.push(node1.data.name);
    await queryParent(node1);
  }
};
const showInstance = async row => {
  let sourceInfo = {};
  sourceInfo["editType"] = "show";
  sourceInfo["tabName"] = [
    "资产信息",
    "资产关系拓扑",
    "资产漏洞概览",
    "安全风险告警"
  ];
  sourceInfo["instanceId"] = row._id;
  sourceInfo["categoryId"] = row.category_id;
  sourceInfo["row"] = row;
  tu.value = await queryTreeUrl(row.category_id);
  sourceInfo["infoTitle"] = tu.value;
  sourceInfo["selectCate"] = "-1";
  emit("source-select", sourceInfo);
  jumpTo("assetInformationPage");
};
const onlienCollection = row => {
  validataInstanceOnlineAxios(row._id)
    .then(res => {
      if (res["data"].result == "success") {
        ElMessage.success("探测完成，结果：" + res["data"].msg);
        // queryFunction();
        initTable();
      } else {
        ElMessage.error(res["data"].msg);
      }
    })
    .catch(exp => {
      console.log(exp);
    });
};
const updateInstance = async row => {
  console.log(row);
  let sourceInfo = {};
  sourceInfo["editType"] = "edit";
  sourceInfo["tabName"] = [
    "资产信息",
    "资产关系拓扑",
    "资产漏洞概览",
    "安全风险告警"
  ];
  sourceInfo["instanceId"] = row._id;
  sourceInfo["categoryId"] = row.category_id;
  sourceInfo["row"] = row;
  tu.value = await queryTreeUrl(row.category_id);
  sourceInfo["infoTitle"] = tu.value;
  sourceInfo["selectCate"] = "-1";
  emit("source-select", sourceInfo);
  jumpTo("assetInformationPage");
};
const deleteInstance = selectIds => {
  ElMessageBox.confirm("是否确认删除?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      deleteInstances(selectIds);
    })
    .catch(() => {});
};
const propv = ref({});
const initTable = () => {
  const json = {
    categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
    pageNum: state.pagination.currentPage,
    pageSize: state.pagination.pageSize,
    isAuth: "true"
  };
  if (state.selectedCard != "all") {
    json["scriptLabel"] = state.selectedCard;
  }
  initTableData(json);
};
const deleteInstances = select => {
  try {
    deleteInstanceByIds(select).then(res => {
      console.log(res);
      if (res?.["data"] == "success") {
        ElMessage.success("删除成功");
      } else {
        ElMessage.warning("删除失败");
      }
      requestTreeDataNewMethod();
    });
  } catch (error) {
    console.log(error);
    ElMessage.warning("删除失败");
  }
};

const queryFunction = () => {
  let param = {
    categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
    pageNum: 1,
    pageSize: state.pagination.pageSize,
    isAuth: "true"
  };
  state.pagination.currentPage = 1;
  //this.pageSize=10;
  if (state.isShow == false) {
    state.queryIsShow = false;
    param["all"] = "all";
    param["allName"] = state.queryConditionForm["allName"];
  } else {
    state.queryIsShow = true;
    let propertyForm = state.propertyForm["rows"];
    if (null != propertyForm) {
      //动态条件
      for (let x = 0; x < propertyForm.length; x++) {
        let colList = propertyForm[x].colList;
        for (let y = 0; y < colList.length; y++) {
          let col = colList[y];
          if (null != col.field) {
            let code = col.field.code;
            let value = col.field.value;
            let showType = col.field.showType;
            let code_id = code + "---" + showType;
            if (
              showType == "input" ||
              showType == "inputSelect" ||
              showType == "moreInput" ||
              showType == "textarea"
            ) {
              code_id = code + "---" + showType + "---" + col.field.hide;
            }
            if (value != undefined && null != code && code != "") {
              if (col.field.value instanceof Array) {
                if (col.field.value[0] != "") {
                  param[code_id] = value.join(",");
                }
              } else {
                param[code_id] = value;
              }
            }
          }
        }
      }
    } else {
      param["name"] = state.queryConditionForm["name"];
      param["code"] = state.queryConditionForm["code"];
      param["manager"] = state.queryConditionForm["manager"];
      param["status"] = state.queryConditionForm["status"];
    }
    let ipaddr = state.querySwithForm.ipaddr;
    let yanma = state.querySwithForm.yanma;
    let startIp = state.querySwithForm.startIp;
    let endIp = state.querySwithForm.endIp;
    if (ipaddr != "") {
      param["query_yanma_startIp"] = iptolong(ipaddr);
      if (yanma != "") {
        let cc = subnet_mask_change_ip_segment(ipaddr, yanma);
        param["query_yanma_startIp"] = cc.split("#")[0];
        param["query_yanma_endIp"] = cc.split("#")[1];
      }
    }
    if (startIp != "") {
      param["query_startIp"] = iptolong(startIp);
    }
    if (endIp != "") {
      param["query_endIp"] = iptolong(endIp);
    }
  }
  initTableData(param);
};
const reset = json => {
  for (let key in json) {
    json[key] = "";
  }
};
const objTableRefs = ref(new Map());
const getObjRef = (el: any, code: String) => {
  objTableRefs.value.set("obj_" + code, el);
};
const resetFunction = () => {
  state.confirmStatusCombobox = null;
  state.individualization_gradStatus = null;
  state.initAssetDuty = null;
  reset(state.queryConditionForm);
  state.querySwithForm.ipaddr = "";
  state.querySwithForm.yanma = "";
  state.querySwithForm.startIp = "";
  state.querySwithForm.endIp = "";
  state.colorMap = {};
  let propertyForm = state.propertyForm["rows"];
  if (null != propertyForm) {
    //动态条件
    for (let x = 0; x < propertyForm.length; x++) {
      let colList = propertyForm[x].colList;
      for (let y = 0; y < colList.length; y++) {
        let col = colList[y];
        if (null != col.field) {
          if (col.field.showType == "comboTree") {
            col.field.value = null;
          } else if (col.field.showType == "number") {
            col.field.value = undefined;
          } else if (col.field.showType == "objectTable") {
            col.field.value = "";
            let formCode = "obj_" + col.field.code;
            objTableRefs.value.get(formCode).reset();
          } else {
            col.field.value = "";
          }
        }
      }
    }
  }
  state.querySwithForm["zoneName"] = null;
  queryFunction();
};
const clickShow = () => {
  state.isShow = !state.isShow;
  const json = {
    categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
    isAuth: "true"
  };
  if (state.isShow) {
    if (!state.querySwithForm.ipaddr) {
      state.querySwithForm.ipaddr = "";
    }
    if (!state.querySwithForm.yanma) {
      state.querySwithForm.yanma = "";
    }
    if (!state.querySwithForm.endIp) {
      state.querySwithForm.endIp = "";
    }
    if (!state.querySwithForm.startIp) {
      state.querySwithForm.startIp = "";
    }
    initCondition(json);
  }
};
const initCondition = async json => {
  state.queryLoading = true;
  initConditionMethod(json)
    .then(res => {
      let newProperty = [];
      if (res["data"]) {
        newProperty = res["data"];
        let oldProperty = state.propertyForm["rows"];
        let old = [];
        if (newProperty["rows"]) {
          for (let i = 0; i < newProperty["rows"].length; i++) {
            for (let ii = 0; ii < newProperty["rows"][i].colList.length; ii++) {
              if (
                newProperty["rows"][i].colList[ii].field.showType == "comboTree"
              ) {
                let tt = [];
                tt.push(
                  ...buildTree(
                    newProperty["rows"][i].colList[ii].field.enumArray,
                    "id",
                    "parentId"
                  )
                );
                newProperty["rows"][i].colList[ii].field.enumArray = JSON.parse(
                  JSON.stringify(tt)
                );
              }
            }
          }
        }
        if (newProperty["rows"] && oldProperty) {
          for (let k = 0; k < oldProperty.length; k++) {
            for (let kk = 0; kk < oldProperty[k]?.colList.length; kk++) {
              if (oldProperty[k]?.colList[kk].field.value) {
                old.push(oldProperty[k]?.colList[kk]);
              }
            }
          }
          for (let i = 0; i < newProperty["rows"].length; i++) {
            for (let ii = 0; ii < newProperty["rows"][i].colList.length; ii++) {
              for (let j = 0; j < old.length; j++) {
                if (
                  newProperty["rows"][i].colList[ii].field.code ==
                  old[j].field.code
                ) {
                  newProperty["rows"][i].colList[ii].field.value =
                    old[j].field.value;
                  break;
                }
              }
            }
          }
        }
      }
      state.queryLoading = false;
      state.propertyForm = newProperty;
      state.ip_mask_state = res["data"].ip_mask_state;
      state.ip_seg_state = res["data"].ip_seg_state;
    })
    .catch(exp => {
      ElMessage.error("查询条件加载失败！", exp);
      state.queryLoading = false;
    });
};
const clickSetQuery = () => {
  state.conditionShow = true;
  state.conditionName = "querySet";
};

const initTopoProperty = async node => {
  queryPropertyByIdAxios(node.id)
    .then(res => {
      state.tableData = res["data"];
      let params = {
        id: node.id
      };
      initAssetPositionSetAxios(params)
        .then(res => {
          if (res["data"]) {
            state.assetPositionForm.positionImage = res["data"].position_image;
            state.assetPositionForm.topoImage = res["data"].topo_image;
            if (res["data"].check_property) {
              state.assetPositionForm.checkProperty =
                res["data"].check_property.split(",");
            }
          }
        })
        .catch(exp => {
          ElMessage.error("加载失败," + exp.message);
        });
    })
    .catch(exp => {
      ElMessage.error("加载失败," + exp.message);
    });
};
const closePopover = column => {
  propRefs.value[
    "popover-" + state.page.currentNodeId
      ? state.page.currentNodeId
      : "0" + "-" + column.prop
  ].hide();
  propv.value[column.prop] = false;
  // let key = 'popover-'+state.page.currentNodeId?state.page.currentNodeId:'0'+'-'+column.prop;
  // if(filterRefs.value.get(key)){
  //   if(filterRefs.value.get(key) instanceof Array){
  //     for(let i=0;i<filterRefs.value.get(key).length;i++){
  //       filterRefs.value.get(key)[i].hide();
  //     }
  //   }else{
  //     filterRefs.value.get(key).hide();
  //   }
  // }
};

const propRefs = ref({});
const closeWindowTree = item => {
  state.windowTreeDialog = false;
  state.treeItem = item;
};
const emit = defineEmits(["jump-to", "source-select"]);
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
const queryCondition = ref();
const handlePageCurrentChange = (page: number) => {
  state.pagination.currentPage = page;
  initTable();
};
const handlePageSizeChange = (page: number) => {
  state.pagination.pageSize = page;
  state.pagination.currentPage = 1;
  initTable();
};
const state = reactive({
  relationCategory: {},
  relationPropertyShow: false,
  topoList: [
    { value: "3", label: "网元" },
    { value: "2", label: "链路" },
    { value: "1", label: "端口" },
    { value: "0", label: "其他" }
  ],
  editCategoryShow: false,
  categoryData: [],
  tableOption: {
    index: false,
    indexLabel: "序号",
    align: "center",
    menuAlign: "center",
    maxHeight: 560,
    selection: true,
    selectionFixed: true,
    refreshBtn: false,
    columnBtn: false,
    gridBtn: false,
    menu: false,
    border: true,
    stripe: true,
    addBtn: false,
    editBtn: false,
    delBtn: false,
    menuWidth: 130,
    column: []
  },
  queryLoading: false,
  initAssetDuty: null,
  confirmStatusCombobox: null,
  individualization_gradStatus: null,
  conditionShow: false,
  conditionName: "querySet",
  caseObject: [],
  currentItem: {},
  windoxboxSearch: {},
  propValue: "",
  object: {
    page: 1,
    size: 10
  },
  mulShow_type: "",
  mulOcode: "",
  currentMulItem: {},
  currentMulId: "",
  totalMulW: "",
  windowboxloading: false,
  mulData: [],
  mulCurrentItem: {},
  queryIsShow: false,
  pagination: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  ip_mask_state: "close",
  ip_seg_state: "close",
  querySwithForm: {
    ipaddr: "",
    yanma: "",
    startIp: "",
    endIp: ""
  },
  propertyForm: {},
  isShow: false,
  queryConditionForm: {},
  searchFormInline: {
    value1: [],
    input: "",
    value2: ""
  },
  options: [
    {
      value: "Option1",
      label: "Option1"
    },
    {
      value: "Option2",
      label: "Option2"
    },
    {
      value: "Option3",
      label: "Option3"
    },
    {
      value: "Option4",
      label: "Option4"
    },
    {
      value: "Option5",
      label: "Option5"
    }
  ],
  defaultTime2: [new Date(2000, 1, 1, 12, 0, 0), new Date(2000, 2, 1, 8, 0, 0)],
  defaultProps: {
    children: "children",
    label: "title"
  },
  page: {
    total: 0,
    currentPage: 1,
    pageSize: 10,
    currentNodeId: "",
    deptZoneId: "",
    instanceId: "",
    editType: "",
    currentNodeName: ""
  },
  dataTable: [],
  tmpTreeItemData: [],
  selectTreeValue: "",
  currentNodeKey: "",
  defaultExpandedKeys: [],
  tmpTreeTypeData: [],
  crudTable: null,
  option: {
    // filterBtn: true,
    height: "29rem",
    maxHeight: "29rem",
    menuWidth: 0,
    index: true,
    indexLabel: "序号",
    indexWidth: "40",
    column: [],
    selection: true,
    selectionFixed: true // 固定表头的选择框
  },
  form: {},
  loading: false,
  cards: [],
  selectedCard: "all",
  filterText: "",
  filterColumnMap: {},
  selectInstanceId: "",
  editType: "",
  tableWidth: 566,
  treeItem: {},
  windowTreeDialog: false,
  fieldSort: "",
  order: "",
  tableLoading: false,
  treeLoading: false,
  colorMap: {},
  pageshow: false,
  selectMulDataModal: false,
  setShowColumn: false,
  tabName: "tableTabSet",
  isParentCategory: false,
  tableShow: false,
  primaryEditShow: false,
  primaryForm: {
    categoryId: "0",
    haveBool: "1",
    primaryKey: []
  },
  cateProList: [],
  infoCategoryShow: false,
  infoCategoryData: [],
  infoCategoryOption: {
    index: true,
    indexLabel: "序号",
    align: "center",
    menuAlign: "center",
    maxHeight: 480,
    refreshBtn: false,
    sortable: true,
    columnBtn: false,
    gridBtn: false,
    menu: false,
    border: true,
    stripe: true,
    addBtn: false,
    editBtn: false,
    delBtn: false,
    menuWidth: 130,
    rowKey: "relationCategoryName",
    column: [
      {
        label: "类别名称",
        prop: "relationCategoryName"
      },
      {
        label: "是否显示",
        prop: "checked"
      }
    ]
  },
  topoPropertySetShow: false,
  assetPositionForm: {
    positionImage: "",
    topoImage: "",
    checkProperty: []
  },
  tableData: [],
  imageList: []
});
const assetPositionFormRef = ref();
const {
  searchFormInline,
  defaultProps,
  page,
  dataTable,
  tmpTreeItemData,
  selectTreeValue,
  currentNodeKey,
  defaultExpandedKeys,
  tmpTreeTypeData,
  cards,
  selectedCard,
  filterText,
  queryConditionForm,
  isShow,
  querySwithForm,
  ip_mask_state,
  treeItem,
  windowTreeDialog,
  tableLoading,
  treeLoading,
  topoList,
  ip_seg_state,
  propertyForm,
  tableOption,
  colorMap,
  categoryData,
  relationPropertyShow,
  conditionShow,
  conditionName,
  setShowColumn,
  tabName,
  isParentCategory,
  tableShow,
  primaryEditShow,
  primaryForm,
  cateProList,
  infoCategoryShow,
  infoCategoryData,
  infoCategoryOption,
  topoPropertySetShow,
  assetPositionForm,
  tableData,
  imageList
} = toRefs(state);
const isChildNodes = ref(true);
// 表格
const primaryFormRef = ref();
const buildTree = (
  data: any[],
  deptId: string | number,
  parentId: string | number
) => {
  const map = new Map();
  const rootNodes = [];

  // 首先将所有节点添加到映射中
  data.forEach(item => {
    map.set(item[deptId], { ...item, children: [] });
  });

  // 然后根据parentId构建树形结构
  data.forEach(item => {
    if (item[parentId] == "-1" || item[parentId] == null) {
      rootNodes.push(map.get(item[deptId]));
    } else {
      if (map.has(item[parentId])) {
        map.get(item[parentId]).children.push(map.get(item[deptId]));
      }
    }
  });
  return rootNodes;
};

const initQueryLabelData = async () => {
  try {
    initQueryLabelMethod({
      categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
      isAuth: "true"
    }).then(res => {
      state.cards = res.data;
      if (state.cards && state.cards.length > 0) {
        for (let u = 0; u < state.cards.length; u++) {
          state.cards[u]["count"] = 0;
          let params = {
            categoryId: state.page.currentNodeId
              ? state.page.currentNodeId
              : "0",
            labelId: state.cards[u].value
          };
          if (state.page.deptZoneId) {
            params["zoneName"] = state.page.deptZoneId;
          } else {
            params["zoneName"] = "";
          }
          // if(queryParams.zoneName){
          //   params["zoneName"] = queryParams.zoneName;
          // }else{
          //   params["zoneName"] = "";
          // }
          queryLabelCountAxios(params).then(res1 => {
            state.cards[u]["count"] = res1["data"].count;
          });
        }
      }
      if (queryParams.label) {
        for (let u = 0; u < state.cards.length; u++) {
          if (state.cards[u].label == queryParams.label) {
            state.selectedCard = state.cards[u].value;
          }
        }
      }
    });
  } catch (error) {
    console.log(error);
  }
};

const initTableColums = async params => {
  state.loading = true;
  try {
    // params["showLabel"] = "2";
    initTableColumsMethod(params).then((res: any) => {
      const tmpData = [];
      state.tableOption.column.length = 0;
      res.data.forEach(item => {
        if (!item.hide) {
          tmpData.push({
            resizable: item["resizable"],
            // filters: true,
            filterable: true,
            tip: item["tooltip"],
            align: item["align"],
            width: item["width"],
            disabled: item["hide"],
            label: item["title"],
            prop: item["key"],
            ellipsis: item["ellipsis"],
            overHidden: item["tooltip"],
            showOverflowTooltip: true,
            headerSlot: item["key"] + "-header"
          });
        }
      });
      state.tableOption.column.push(...tmpData, {
        label: "操作",
        prop: "operator",
        width: enableAI.value ? "210px" : "130px",
        fixed: "right"
      });
      state.loading = false;
      initTableData(params);
    });
  } catch (error) {
    console.log(error);
    state.loading = false;
  }
};

function selectCard(card) {
  state.selectedCard = card.value;
  let pararms = {
    categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
    pageNum: state.pagination.currentPage,
    pageSize: state.pagination.pageSize,
    isAuth: "true",
    dept__zone__id: state.page.deptZoneId,
    filterColumnMap: state.colorMap
  };
  if (state.selectedCard != "all") {
    pararms["scriptLabel"] = state.selectedCard;
  }
  initTableData(pararms);
}

// 设置菜单宽度
const setMenuWidth = () => {
  setTimeout(() => {
    let width = 0;
    const list = document.querySelectorAll(".avue-crud__menu");
    list.forEach(ele => {
      const childList = ele.children;
      let allWidth = 0;
      for (let i = 0; i < childList.length; i++) {
        const child = childList[i];
        allWidth += child["offsetWidth"] + 18;
      }
      if (allWidth >= width) width = allWidth;
    });
    state.option.menuWidth = width;
  });
};
// 在组件挂载后执行
onMounted(() => {
  state.imageList = getImages();
  if (props.sourceInfo) {
    state.page.currentNodeId = props.sourceInfo;
  }
  // setMenuWidth();
  // updateWindowWidth();

  requestSelectTreeDataMethod();
  // if(state.page.deptZoneId==undefined){
  //   requestTreeDataNewMethod();
  // }
});
const iptolong = ip => {
  let num = 0;
  ip = ip.split(".");
  num =
    Number(ip[0]) * 256 * 256 * 256 +
    Number(ip[1]) * 256 * 256 +
    Number(ip[2]) * 256 +
    Number(ip[3]);
  num = num >>> 0;
  return num;
};
const subnet_mask_change_ip_segment = (ip_str, mask) => {
  let mark_len = 32;
  mark_len = mask;
  let nextBit = Math.min(mark_len, 8);
  let ips = ip_str.split(".");
  let maskIp = {};
  maskIp["a"] = ((1 << nextBit) - 1) << (8 - nextBit);
  mark_len -= 8;
  nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
  maskIp["b"] = ((1 << nextBit) - 1) << (8 - nextBit);

  mark_len -= 8;
  nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
  maskIp["c"] = ((1 << nextBit) - 1) << (8 - nextBit);

  mark_len -= 8;
  nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
  maskIp["d"] = ((1 << nextBit) - 1) << (8 - nextBit);
  // 开始IP各个位置的值
  let a = ips[0] & maskIp["a"];
  let b = ips[1] & maskIp["b"];
  let c = ips[2] & maskIp["c"];
  let d = ips[3] & maskIp["d"];

  // 开始IP
  let startIp = a + "." + b + "." + c + "." + d;
  // 结束IP各个位置的值
  a = (maskIp["a"] ^ 255) | ips[0];
  b = (maskIp["b"] ^ 255) | ips[1];
  c = (maskIp["c"] ^ 255) | ips[2];
  d = (maskIp["d"] ^ 255) | ips[3];

  // 结束IP
  let endIp = a + "." + b + "." + c + "." + d;
  return iptolong(startIp) + "#" + iptolong(endIp);
};

const requestSelectTreeDataMethod = () => {
  initSelectTreeDataMethod({
    isAuth: "true"
  }).then(res => {
    state.tmpTreeItemData.push(...buildTree(res.data.list, "id", "parentId"));
    if (queryParams.zoneName) {
      selectTreeValue.value = queryParams.zoneName;
    }
  });
};
const initTableData = param => {
  param = Object.assign({}, param, {
    dept__zone__id: state.page.deptZoneId
  });
  param.pageNum = state.pagination.currentPage;
  param.pageSize = state.pagination.pageSize;
  if (state.isShow == false) {
    state.queryIsShow = false;
    param.all = "all";
    param.allName = state.queryConditionForm["allName"];
  } else {
    state.queryIsShow = true;
    let propertyForm = state.propertyForm["rows"];
    if (propertyForm) {
      //动态条件
      for (let x = 0; x < propertyForm.length; x++) {
        let colList = propertyForm[x].colList;
        for (let y = 0; y < colList.length; y++) {
          let col = colList[y];
          if (null != col.field) {
            let code = col.field.code;
            let value = col.field.value;
            let showType = col.field.showType;

            let code_id = code + "---" + showType;
            if (
              showType == "input" ||
              showType == "inputSelect" ||
              showType == "moreInput"
            ) {
              code_id = code + "---" + showType + "---" + col.field.hide;
            }
            if (null != value && code) {
              if (col.field.value instanceof Array) {
                if (value[0] != "" || value[1] != "") {
                  for (let i = 0; i < value.length; i++) {
                    if (!value[i]) {
                      value[i] = "null";
                    }
                  }
                  param[code_id] = value.join(",");
                }
              } else {
                param[code_id] = value;
              }
            }
          }
        }
      }
    } else {
      param.name = state.queryConditionForm["name"];
      param.code = state.queryConditionForm["code"];
      param.manager = state.queryConditionForm["manager"];
      param.status = state.queryConditionForm["status"];
    }
    let ipaddr = state.querySwithForm.ipaddr;
    let yanma = state.querySwithForm.yanma;
    let startIp = state.querySwithForm.startIp;
    let endIp = state.querySwithForm.endIp;
    if (ipaddr != "") {
      param.query_yanma_startIp = iptolong(ipaddr);
      if (yanma != "") {
        let cc = subnet_mask_change_ip_segment(ipaddr, yanma);
        param.query_yanma_startIp = cc.split("#")[0];
        param.query_yanma_endIp = cc.split("#")[1];
      }
    }
    if (startIp != "") {
      param.query_startIp = iptolong(startIp);
    }
    if (endIp != "") {
      param.query_endIp = iptolong(endIp);
    }
  }
  state.tableLoading = true;
  state.treeLoading = true;
  // const loading = this.$loading({
  //   lock: true
  // });
  if (state.order) {
    param.fieldSort = state.fieldSort;
    param.order = state.order;
  }
  if (state.colorMap != null) {
    param.filterColumnMap = JSON.stringify(state.colorMap);
  }
  state.pageshow = false;
  // param.showLabel="2";
  initTableDataMethod(param).then(res => {
    if (res["data"].pageNum) {
      state.pagination.currentPage = res["data"].pageNum;
    }

    nextTick(() => {
      state.pageshow = true;
    });
    setTimeout(() => {
      state.dataTable = res["data"].list;
      state.pagination.total = res["data"].total;
      nextTick(() => {
        state.tableLoading = false;
        state.treeLoading = false;
      });
    }, 1500);
  });
};
const requestTreeDataNewMethod = async (id = "") => {
  console.log(state.selectedCard);
  if (queryParams.label) {
    await initQueryLabelData();
  }
  initTreeDataNewMethod({
    code: "category_id",
    dept__zone__id: id || "",
    isAuth: "true"
  }).then(res => {
    state.tmpTreeTypeData.length = 0;
    state.tmpTreeTypeData.push(...buildTree(res.data, "id", "parentId"));
    // if(!state.page.currentNodeId){
    console.log(queryParams);
    if (!state.page.currentNodeId) {
      if (queryParams.zoneName || queryParams.label) {
        state.page.currentNodeId = state.tmpTreeTypeData[0]["id"];
      } else {
        if (
          state.tmpTreeTypeData[0].children &&
          state.tmpTreeTypeData[0].children.length > 0
        ) {
          if (
            state.tmpTreeTypeData[0].children[0].children &&
            state.tmpTreeTypeData[0].children[0].children.length > 0
          ) {
            state.page.currentNodeId =
              state.tmpTreeTypeData[0].children[0].children[0]["id"];
          } else {
            state.page.currentNodeId =
              state.tmpTreeTypeData[0].children[0]["id"];
          }
        } else {
          state.page.currentNodeId = state.tmpTreeTypeData[0]["id"];
        }
      }
    }
    // }
    state.currentNodeKey = state.page.currentNodeId;
    console.log(state.currentNodeKey);
    if (!queryParams.label) {
      initQueryLabelData();
    }
    nextTick(() => {
      if (state.tmpTreeTypeData[0]["id"]) {
        state.defaultExpandedKeys.push(state.tmpTreeTypeData[0]["id"]);
      } else {
        state.defaultExpandedKeys.push(state.currentNodeKey);
      }
      state.currentNodeKey = state.page.currentNodeId;
      treeRef.value.setCurrentKey(state.page.currentNodeId);
      isChildNodes.value = treeRef.value.getCurrentNode().children.length > 0;
    });
    let params = {
      categoryId: state.page.currentNodeId,
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      isAuth: "true",
      dept__zone__id: state.page.deptZoneId,
      filterColumnMap: state.colorMap
    };

    if (state.selectedCard != "all") {
      params["scriptLabel"] = state.selectedCard;
    }
    initTableColums(params);
  });
};

watch(
  selectTreeValue,
  val => {
    if (!val) {
      state.page.deptZoneId = "";
    } else {
      state.page.deptZoneId = val;
    }
    state.selectedCard = null;
    requestTreeDataNewMethod(val);

    // console.log("触发监听",val,oldValue);
  },
  { deep: true, immediate: true }
);
const nodeClick = node => {
  state.colorMap = {};
  // console.log(node.id);
  isChildNodes.value = node.children.length > 0;
  state.isShow = false;
  state.selectedCard = "all";
  state.page.currentNodeId = node.id;
  state.page.currentNodeName = node.name;
  state.pagination.currentPage = 1;
  state.pagination.pageSize = 20;
  initQueryLabelData();
  let params = {
    categoryId: state.page.currentNodeId,
    pageNum: state.pagination.currentPage,
    pageSize: state.pagination.pageSize,
    isAuth: "true",
    dept__zone__id: state.page.deptZoneId,
    filterColumnMap: state.colorMap
  };
  if (state.selectedCard != "all") {
    params["scriptLabel"] = state.selectedCard;
  }
  initTableColums(params);
};

interface Tree {
  [key: string]: any;

  id: number;
  label: string;
  children?: Tree[];
}

const treeRef = ref<InstanceType<typeof ElTree>>();

watch(filterText, val => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};

const homeBottomLeftRef = ref(null);
window.addEventListener("resize", updateWindowWidth);

function updateWindowWidth() {
  if (homeBottomLeftRef.value) {
    state.tableWidth =
      window.innerWidth - homeBottomLeftRef.value.$el.offsetWidth - 266;
  }
}

// const assetInfoVisable = ref(false);
const newAddAssets = async () => {
  let sourceInfo = {};
  sourceInfo["editType"] = "add";
  sourceInfo["instanceId"] = 0;
  sourceInfo["tabName"] = ["资产信息"];
  sourceInfo["categoryId"] = state.page.currentNodeId
    ? state.page.currentNodeId
    : "0";
  tu.value = await queryTreeUrl(
    state.page.currentNodeId ? state.page.currentNodeId : "0"
  );
  sourceInfo["infoTitle"] = tu.value;
  sourceInfo["selectCate"] = "-1";
  sourceSelect(sourceInfo);
  jumpTo("assetInformationPage");
  // centerDialogVisible.value = true;
  // assetInfoRef.value.openVis();
};

const sourceSelect = sourceInfo => {
  emit("source-select", sourceInfo);
};

// 导入抽屉
const importShow = ref(false);
const closeAssetImport = () => {
  importShow.value = false;
  requestTreeDataNewMethod();
};

// 导出
const allExportHandle = {
  // 所有导出的相关函数
  exportFunction: command => {
    if (command == "exportCate") {
      try {
        exportInstance({
          categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
          isAuth: "true",
          type: "exportCate"
        }).then(() => {});
      } catch (error) {
        console.log(error);
      }
    } else {
      let param = {
        categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
        isAuth: "true"
      };
      if (state.selectedCard != "all") {
        param["scriptLabel"] = state.selectedCard;
      }
      if (state.isShow == false) {
        state.queryIsShow = false;
        param["all"] = "all";
        param["allName"] = state.queryConditionForm["allName"];
      } else {
        state.queryIsShow = true;
        let propertyForm = state.propertyForm["rows"];
        if (propertyForm) {
          //动态条件
          for (let x = 0; x < propertyForm.length; x++) {
            let colList = propertyForm[x].colList;

            for (let y = 0; y < colList.length; y++) {
              let col = colList[y];
              if (null != col.field) {
                let code = col.field.code;
                let value = col.field.value;
                let showType = col.field.showType;

                let code_id = code + "---" + showType;
                if (null != value && code) {
                  if (col.field.value instanceof Array) {
                    if (col.field.value[0] != "") {
                      param[code_id] = value.join(",");
                    }
                  } else {
                    param[code_id] = value;
                  }
                }
              }
            }
          }
        } else {
          param["name"] = state.queryConditionForm.name;
          param["code"] = state.queryConditionForm.code;
          param["manager"] = state.queryConditionForm.manager;
          param["status"] = state.queryConditionForm.status;
        }
      }
      exportInstance(param)
        .then(() => {
          ElMessage.success("导出成功");
        })
        .catch(() => {
          ElMessage.error("导出失败，请联系管理员");
        });
    }
  },
  exportAll: () => {
    try {
      exportInstance({
        categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
        isAuth: "true"
      }).then(() => {});
    } catch (error) {
      console.log(error);
    }
  },
  exportSelect: () => {
    // selectedTableData.value
    try {
      exportInstance({
        categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
        isAuth: "true",
        selectInstance: selectedTableData.value.join(",")
      }).then(() => {});
    } catch (error) {
      console.log(error);
    }
  },
  exportTemp: () => {
    // exportInsTemp
    try {
      exportInsTemp({
        categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0"
      }).then(() => {});
    } catch (error) {
      console.log(error);
    }
  },
  exportRelation: () => {
    try {
      exportRelationReportAxios({
        categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
        isAuth: "true"
      }).then(() => {});
    } catch (error) {
      console.log(error);
    }
  }
};
const selectedTableData = ref([]);

const handleCommand = command => {
  if (command == "exportCate") {
    allExportHandle.exportFunction(command);
  } else if (command == "exportAll") {
    allExportHandle.exportAll();
  } else if (command == "exportSearch") {
    allExportHandle.exportFunction(command);
  } else if (command == "exportSelect") {
    if (selectedTableData.value.length > 0) {
      allExportHandle.exportSelect();
    } else {
      ElMessage.info("请先勾选需要导出的记录");
    }
  } else if (command == "exportTemp") {
    allExportHandle.exportTemp();
  } else if (command == "exportRelation") {
    allExportHandle.exportRelation();
  }
};

// 更多操作
const moreOperationsDialog = ref(false);
const closeSetShowGroup = () => {
  moreOperationsDialog.value = false;
};
const clearWindowTree = item => {
  item.field.showValue = "";
  item.field.value = "";
  state.windowTreeDialog = true;
  state.windowTreeDialog = false;
};
const filterTableRef = ref(null);
const more_update_property = ref(false);
const handleMoreCommand = command => {
  if (command == "batchDeletion") {
    // console.log(filterTableRef.value.exposeTableDeleteMethod, selectedTableData.value);
    ElMessageBox.confirm("确认要删除吗?", "", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        // 用户点击了确定按钮
        deleteInstances(selectedTableData.value.join(","));
      })
      .catch(() => {});
  } else if (command == "batchModification") {
    validateMoreUpdateAxios({
      ids: selectedTableData.value.join(",")
    })
      .then(res => {
        console.log(res);
        if (res["data"]["result"] == "error") {
          ElMessage.error(`${res["data"]["message"]}`);
        } else if (res["data"]["result"] == "success") {
          more_update_property.value = true;
        }
      })
      .catch(exp => {
        ElMessage.error("校验失败," + exp.message);
      })
      .finally(() => {});
  } else if (command == "clickSetQuery") {
    clickSetQuery();
  } else if (command == "showInstanceDelete") {
    sourceSelect(state.page.currentNodeId ? state.page.currentNodeId : "0");
    jumpTo("instanceDelete");
  }
};
const openWindowTree = item => {
  let tt = "";
  if (item.field.showType == "windowboxTreeFilter") {
    if (item.field.value != null && item.field.value.length > 0) {
      for (let i = 0; i < item.field.value.length; i++) {
        if (i == item.field.value.length - 1) {
          tt += item.field.value[i];
        } else {
          tt += item.field.value[i];
        }
      }
      item.field.showValue = tt;
    }
  }
  state.treeItem = {};
  state.treeItem = item;
  state.windowTreeDialog = true;
};
const windowMulTable = ref();
const openFilterSelect = item => {
  item.field.showValue = "";
  if (item.field.value != null && item.field.value.length > 0) {
    for (let i = 0; i < item.field.value.length; i++) {
      if (i == item.field.value.length - 1) {
        item.field.showValue += item.field.value[i];
      } else {
        item.field.showValue += item.field.value[i] + ",";
      }
    }
  }
  state.windowboxloading = true;
  state.mulCurrentItem = {};
  state.mulCurrentItem["value"] = JSON.parse(JSON.stringify(item.field.value));
  state.mulCurrentItem["label"] = JSON.parse(
    JSON.stringify(item.field.showValue)
  );
  state.currentMulItem = item;
  state.currentMulId = item.field.value;
  state.mulOcode = item.field.ocode;
  state.mulShow_type = item.field.showType;
  let params = {
    show_type: "windowbox",
    ocode: item.field.ocode,
    pageSize: state.object.size,
    pageNum: state.object.page,
    value: state.propValue
  };
  queryEnumList(params).then(res => {
    state.selectMulDataModal = true;
    state.mulData = res.data.data;
    state.totalMulW = res.data.total;
    state.windowboxloading = false;
    let windowMulSelects = [];

    let ttValue = null;
    let ttLabel = null;
    if (state.mulCurrentItem["value"] != null) {
      if (typeof state.mulCurrentItem["value"] == "string") {
        ttValue = state.mulCurrentItem["value"].split(",");
      } else {
        ttValue = state.mulCurrentItem["value"];
      }
      ttLabel = state.mulCurrentItem["label"].split(",");
    }
    if (ttValue != null) {
      for (let i = 0; i < ttValue.length; i++) {
        let cc = {
          value: ttValue[i],
          label: ttLabel[i]
        };
        windowMulSelects.push(cc);
      }
    }

    nextTick(() => {
      if (windowMulSelects.length > 0) {
        for (let k = 0; k < windowMulSelects.length; k++) {
          for (let i = 0; i < state.mulData.length; i++) {
            if (state.mulData[i].value == windowMulSelects[k].value) {
              windowMulTable.value.toggleRowSelection(state.mulData[i], true);
              ttValue.splice(ttValue.indexOf(windowMulSelects[k].value), 1);
              ttLabel.splice(ttLabel.indexOf(windowMulSelects[k].label), 1);
            }
          }
        }
        state.mulCurrentItem["value"] = ttValue.join(",");
        state.mulCurrentItem["label"] = ttLabel.join(",");
      }
    });
  });
};
const clearTableList = (event, item) => {
  let query = "";
  if (
    state.windoxboxSearch[item.field.code + "windowbox"] != undefined &&
    state.windoxboxSearch[item.field.code + "windowbox"] != null
  ) {
    query = state.windoxboxSearch[item.field.code + "windowbox"];
  }
  item.field.tableList = [];
  remoteMethod(query, item);
};
const remoteMethod = (query, item) => {
  state.windoxboxSearch[item.field.code + "windowbox"] = query;
  if (query !== "") {
    //this.loading = true;
    //this.loading = false;
    item.field.tableList = item.field.enumArray.filter(item1 => {
      return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
    });
  } else {
    item.field.tableList = [];
  }
};
const clearSelect = item => {
  item.field.value = "";
  item.field.showValue = "";
  state.currentItem["field"].value = "";
  state.currentItem["field"].showValue = "";
  console.info("value " + state.currentItem["field"].value);
  if (state.currentItem["field"].isCascade === 1) {
    //如果是主动发起的需要更新缓存
    updateActiveCase(state.currentItem);
  }
};
const updateActiveCase = item => {
  if (state.caseObject.length > 0) {
    let obj = state.caseObject.filter(p => {
      //根据当前变动的主动发起关联的属性，遍历已缓存的所有主动关联的key和value
      return p.key == item.field.code;
    });
    if (obj != null && obj.length > 0 && undefined != item.field.value) {
      //如果此属性存在，更新值
      obj[0].value = item.field.value;
    } else {
      //如果不存在，放入主动发起的缓存中
      let json = {
        key: item.field.code,
        value: item.field.value
      };
      state.caseObject.push(json);
    }
  } else {
    let json = {
      key: item.field.code,
      value: item.field.value
    };
    state.caseObject.push(json);
  }
};
const clearTreeTableList = (event, item) => {
  let query = "";
  if (
    state.windoxboxSearch[item.field.code + "windowboxTree"] != undefined &&
    state.windoxboxSearch[item.field.code + "windowboxTree"] != null
  ) {
    query = state.windoxboxSearch[item.field.code + "windowboxTree"];
  }
  item.field.tableList = [];
  remoteTreeMethod(query, item);
};
const remoteTreeMethod = (query, item) => {
  state.windoxboxSearch[item.field.code + "windowboxTree"] = query;
  if (query !== "") {
    //this.loading = true;
    //this.loading = false;
    item.field.tableList = item.field.enumArray[0].enumList.filter(item1 => {
      return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
    });
  } else {
    item.field.tableList = [];
  }
};
const onClickOutside = () => {
  for (const key in propv.value) {
    propv.value[key] = false;
  }
};
const loadColumnFilter = (key, column) => {
  propv.value[column.prop] = true;
  for (const key in propv.value) {
    if (key != column.prop) {
      propv.value[key] = false;
    }
  }

  console.log(propv.value);
  // nextTick(()=>{
  //   if(filterRefs.value.get(key) instanceof Array){
  //     filterRefs.value.get(key)[0].updateShowPo(true);
  //   }else{
  //     filterRefs.value.get(key).updateShowPo(true);
  //   }
  // })
};
const updateCategory = id => {
  getCategoryByIdAxios(id).then(res => {
    state.categoryData = res["data"];
    state.editCategoryShow = true;
  });
};
const selectCateName = ref("");
const categoryCommand = (command, data) => {
  if (command == "updateCategory") {
    updateCategory(data.id);
  } else if (command == "relationProperty") {
    updateRelationProperty(data);
  } else if (command == "deleteCategory") {
    deleteCategory(data);
  } else if (command == "primaryUpdate") {
    primaryUpdate(data);
  } else if (command == "infoCategory") {
    infoCategoryEdit(data);
  } else if (command == "groupSetting") {
    selectCate.value = data.id;
    selectCateName.value = data.name;
    moreOperationsDialog.value = true;
  } else if (command == "clickSetColumn") {
    editSetColumn(data);
  } else if (command == "topoPropertySet") {
    topoPropertySet(data);
  }
};

const topoPropertySet = data => {
  initTopoProperty(data);
  selectCate.value = data.id;
  selectCateName.value = data.name;
  state.topoPropertySetShow = true;
};
const infoCategoryEdit = data => {
  queryInfoRelationCategorySetting(data.id).then(res => {
    if (res["data"].result) {
      ElMessage.error(res["data"].msg);
    } else {
      state.infoCategoryData = res["data"].list;
      state.infoCategoryShow = true;
    }
  });
};
const updateRelationProperty = data => {
  state.relationCategory = data;
  state.relationPropertyShow = true;
};
const deleteCategory = data => {
  categoryVerifyDelAxios(data.id).then(res => {
    if (res["data"].state === "OK") {
      ElMessageBox.confirm(
        "删除类别时，该类别下的所有类别将同时被删除,确认删除吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          deleteCategoryByIdAxios(data.id).then(res1 => {
            if (res1.data == "success") {
              ElMessage.success("删除成功");
              requestTreeDataNewMethod();
            }
          });
        })
        .catch(() => {
          ElMessage.info("已取消删除");
        });
    } else if (res["data"].state === "CONFIRM") {
      ElMessageBox.confirm(
        res["data"].message +
          "删除类别时，该类别下的所有类别将同时被删除！确认删除吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          deleteCategoryByIdAxios(data.id).then(res1 => {
            if (res1.data == "success") {
              ElMessage.success("删除成功");
              requestTreeDataNewMethod();
            }
          });
        })
        .catch(() => {
          ElMessage.info("已取消删除");
        });
    } else if (res["data"].state === "ERROR") {
      ElMessage.error("当前类别或子类别下已存在资产！");
    }
  });
};
const instanceConditionRef = ref();
const saveCondition = () => {
  instanceConditionRef.value.saveCondition();
};
const cancalConditionDialog = () => {
  instanceConditionRef.value.cancalCondition();
};
const cancalCondition = () => {
  state.conditionName = "querySet";
  state.conditionShow = false;
  if (state.isShow) {
    const json = {
      categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
      isAuth: "true"
    };
    initCondition(json);
  }
};
const closeShowColumn = () => {
  state.setShowColumn = false;
  state.tabName = "tableTabSet";
  const json = {
    categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
    pageNum: 1,
    pageSize: 10,
    isAuth: "true"
  };
  initTableColums(json);
};
const tableShowClick = ss => {
  if (ss == "1") {
    state.tableShow = true;
  } else {
    state.tableShow = false;
  }
};
const editSetColumn = data => {
  if (data.id === "0") {
    state.isParentCategory = false;
  } else {
    if (data.children != null && data.children.length > 0) {
      state.isParentCategory = false;
    } else {
      state.isParentCategory = true;
    }
  }
  selectCate.value = data.id;
  selectCateName.value = data.name;
  state.setShowColumn = true;
  state.tabName = "tableTabSet";
};
const chagenSelection = selection => {
  selectedTableData.value = [];
  if (selection && selection.length > 0) {
    for (let i = 0; i < selection.length; i++) {
      selectedTableData.value.push(selection[i]["_id"]);
    }
  }
};
const savePrimary = () => {
  primaryFormRef.value.validate(valid => {
    if (valid) {
      let params = {
        categoryId: state.primaryForm.categoryId,
        haveBool: state.primaryForm.haveBool,
        primaryKey: state.primaryForm.primaryKey.join(",")
      };
      saveCategoryPrimary(params).then(() => {
        ElMessage.success("保存成功");
      });
    } else {
      ElMessage.error("表单校验不通过");
    }
  });
};
const primaryUpdate = data => {
  state.primaryForm.categoryId = data.id;
  queryCategoryPrimary(data.id).then(res => {
    if (res["data"]) {
      state.primaryForm.haveBool = res["data"].haveBool;
      state.primaryForm.primaryKey = res["data"].primaryKey.split(",");
    } else {
      state.primaryForm.haveBool = "1";
      state.primaryForm.primaryKey = [];
    }
    let pp = {
      categoryId: data.id
    };
    queryPropertyByCategory(pp).then(res => {
      if (res["data"]) {
        state.cateProList = res["data"];
      }
    });
    state.primaryEditShow = true;
  });
};
const selectCate = ref("0");
const stopClick = () => {};
const changeFilterSort = columnSort => {
  let column = columnSort.column;
  let order = columnSort.order;

  if (order == null) {
    state.fieldSort = null;
    state.order = null;
    const json = {
      categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      isAuth: "true"
    };
    if (state.selectedCard != "all") {
      json["scriptLabel"] = state.selectedCard;
    }
    initTableData(json);
  } else if (order == "asc") {
    state.fieldSort = column.prop;
    state.order = "ASC";
    const json = {
      categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      isAuth: "true"
    };
    if (state.selectedCard != "all") {
      json["scriptLabel"] = state.selectedCard;
    }
    initTableData(json);
  } else {
    state.fieldSort = column.prop;
    state.order = "DESC";
    const json = {
      categoryId: state.page.currentNodeId ? state.page.currentNodeId : "0",
      pageNum: state.pagination.currentPage,
      pageSize: state.pagination.pageSize,
      isAuth: "true"
    };
    if (state.selectedCard != "all") {
      json["scriptLabel"] = state.selectedCard;
    }
    initTableData(json);
  }
};

const changeSort = (newIndex, oldIndex) => {
  let newRow = state.infoCategoryData[newIndex];
  let oldRow = state.infoCategoryData[oldIndex];
  state.infoCategoryData[newIndex] = oldRow;
  state.infoCategoryData[oldIndex] = newRow;
};
const saveInfoCategoryRelation = () => {
  let params = {
    list: state.infoCategoryData,
    categoryId: state.infoCategoryData[0]["categoryId"]
  };
  saveInfoRelationCategorySetting(params).then(res => {
    if (res.data.result == "success") {
      ElMessage.success("保存成功");
      state.infoCategoryShow = false;
    } else {
      ElMessage.error(res.data.msg);
    }
  });
};
const instanceShowColumnRef = ref();
const detailShowSetRef = ref();
const saveTable = () => {
  if (state.tabName == "tableTabSet") {
    instanceShowColumnRef.value.saveTable();
  } else {
    detailShowSetRef.value.saveChecked();
  }
};
const closeDialog = () => {
  if (state.tabName == "tableTabSet") {
    instanceShowColumnRef.value.closeDialog();
  } else {
    detailShowSetRef.value.closeChecked();
  }
};
const saveTopoProperty = () => {
  if (
    state.assetPositionForm.positionImage == null ||
    state.assetPositionForm.positionImage == ""
  ) {
    ElMessage.error("请选择定位图标");
    return;
  }
  if (
    state.assetPositionForm.topoImage == null ||
    state.assetPositionForm.topoImage == ""
  ) {
    ElMessage.error("请选择拓扑图标");
    return;
  }
  if (
    state.assetPositionForm.checkProperty == null ||
    state.assetPositionForm.checkProperty.length == 0
  ) {
    ElMessage.error("请选择展示属性");
    return;
  }

  let params = {
    id: selectCate.value,
    position_image: state.assetPositionForm.positionImage,
    topo_image: state.assetPositionForm.topoImage,
    check_property: state.assetPositionForm.checkProperty.join(",")
  };
  saveAssetPositionSetAxios(params)
    .then(res => {
      if (res.data.result == "success") {
        ElMessage.success("保存成功");
      } else {
        ElMessage.error(res.data.message);
      }
      state.topoPropertySetShow = false;
    })
    .catch(exp => {
      ElMessage.error("保存失败，" + exp.message);
    });
};
</script>

<style lang="scss" scoped>
.instance {
  padding-top: 10px;
  :deep(.avue-crud__header) {
    display: none;
  }
  .expandDiv {
    :deep(.el-form-item) {
      margin-bottom: 5px;
    }
    :deep(.el-input--small) {
      width: 23rem;
    }
    :deep(.el-select) {
      width: 23rem;
    }
    :deep(.el-select__wrapper) {
      width: 23rem;
    }
    :deep(.el-select__selection) {
      width: 21rem;
    }
    :deep(.el-form-item__label) {
      font-weight: 400;
    }
  }

  :deep(.instance-form-inline) {
    .el-select__selection {
      min-height: 1.5rem;
    }
    .el-form-item__label {
      height: 1.9rem;
      line-height: 2rem;
      color: #999;
      font-size: 14px;
    }
    .el-input__inner {
      height: 1.5rem;
      line-height: 1.5rem;
    }
    .el-input-group__append {
      background-color: #409eff;
      color: #fff;
      height: 1.6rem;
      line-height: 1.6rem;
      font-size: 12px;
    }
    .el-button {
      height: 1.5rem;
      line-height: 1.5rem;
    }
    .dropdown-menu {
      .el-dropdown__list {
        padding: 0.2rem;
      }
    }
    .el-row {
      justify-content: left;
      // flex-wrap: nowrap
    }
  }

  :deep(.centerCard) {
    > div {
      // box-shadow: 1px 1px 6px 0px #eee;
      text-align: center;
      // line-height: 1.9rem;

      span {
        font-size: 13px;
        font-weight: 600;
        // color: rgb(153, 153, 153);
      }

      div {
        font-weight: 500;
        // color: rgb(67, 67, 67);
        // font-size: 20px;
        overflow: auto;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .selected {
      box-sizing: border-box;
      // border: 1px solid #409eff;
      background: #409eff;

      span,
      div {
        color: #fff;
        // color: #fff;
      }
    }
  }

  :deep(.bottomLR) {
    justify-content: start;
    margin-left: 0.55em;

    .el-select__wrapper {
      height: 1.5rem;
      line-height: 1.5rem;
      width: 100%;
      font-size: 14px;
    }

    .bottomRTable .el-select__wrapper {
      width: auto;
    }

    .bottomRTable .el-table__row {
      height: 3rem;
    }

    .bottomRTable .el-checkbox-group .el-row {
      height: 29rem;
    }
  }
}
:deep(.el-transfer-panel__item).el-checkbox {
  margin-right: 10px;
  .transferLabel {
    display: flex;
    justify-content: space-between !important;
  }
}
.el-transfer :deep(.el-transfer-pane):first-child .sort {
  display: none;
}
.moving {
  border-bottom: 1px solid #409eff;
}
.movingTop {
  border-top: 1px solid #409eff;
}
.movingBottom {
  border-bottom: 1px solid #409eff;
}
</style>
