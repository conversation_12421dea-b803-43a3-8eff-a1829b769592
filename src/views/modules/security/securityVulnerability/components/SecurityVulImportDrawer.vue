<template>
  <!-- <div class="title" style="display: flex; align-items: center" slot="title">
    <span class="label" style="margin-left: 8px">导入</span>
  </div> -->
  <div class="asset-import" style="padding: 0 20px; height: 99%; position: relative; overflow: hidden">
    <el-upload ref="uploader" class="asset-upload" :headers="uploadHeaders" :data="extraParams"
      :on-error="onUploadError" :on-success="onUploadSuccess" :on-progress="onFileProgress"
      :before-upload="beforeUpload" drag :limit="1" accept=".xlsx,.zip"
      :action="`/rest/security-vul/upload/vulManualEntryData`" multiple>
      <svg style="
        width: 97px;
        height: 79px;
        color: #d4d4d4;
        margin: 0 auto;
        " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
        <path fill="currentColor"
          d="M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6z">
        </path>
      </svg>
      <div class="el-upload__text"><em>点击上传文件</em> 或者拖拽上传</div>
      <div class="upload-desc">只能上传excel文件或者zip文件</div>
    </el-upload>

    <div style="display: flex; align-items: center; margin: 5px 0">
      <el-button type="text" @click="exportTemplate">导入模板下载</el-button>
    </div>

    <template v-if="showErrorMsg">
      <el-divider></el-divider>
      <el-alert v-if="exceptionMsg" type="error">{{ exceptionMsg }}</el-alert>
      <template v-else>
        <!--        <el-divider :content="`错误信息（${errorMsgList.length}）`"></el-divider>-->
        <div :title="`错误信息（${errorMsgList.length}）`" style="
              max-height: calc(100% - 460px);
              font-size: 14px;
              overflow: auto;
              padding: 10px;
              background-color: #f9f9f9;
            ">
          <div v-sec-html="`错误信息（${errorMsgList.length}）`" style="color: red"></div>
          <div class="error-items" style="height: calc(100% - 30px); overflow: auto; color: #7f8db5">
            <div class="error-item" v-for="(msg, i) in errorMsgList" :key="i">
              {{ msg }}
            </div>
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { http } from "@/utils/http";

const props = defineProps({
  templateType: String,
})

const uploadHeaders = ref({});
const fileName = ref("未选择");
const percent = ref(0);
const uploaded = ref("");
const total = ref("");
const showUploadProgress = ref(false);
const showErrorMsg = ref(false);
const errorMsgList = ref([]);
const exceptionMsg = ref("");
const uploader = ref(null);

const extraParams = computed(() => {
  return {
    type: props.templateType,
  };
})

const emit = defineEmits(['refresh']);

/** 导出模板 */
const exportTemplate = () => {
  http.request(
    'get',
    `/security-vul/download/vulManualEntryModel?type=${props.templateType}`,
    {},
    {
      responseType: 'blob',
      // headers: {
      //   "Content-Type": "application/json"
      // }
    }
  );
}
const onUploadError = (err, file, fileList) => {
  console.log(err);
  let message = err.message;
  showErrorMsg.value = true;
  try {
    let errData = JSON.parse(message);
    let msg = errData && errData.msg;
    let errorMsgList01 = null;
    try {
      errorMsgList01 = JSON.parse(msg);
      if (Array.isArray(errorMsgList01)) {
        errorMsgList.value = errorMsgList01;
      } else {
        exceptionMsg.value = msg;
      }
    } catch (e) {
      // 服务异常
      exceptionMsg.value = msg;
    }
  } catch (error) {
    exceptionMsg.value = message;
  }
  console.error(exceptionMsg.value);
  uploader.value.clearFiles();
}
const onUploadSuccess = (response, file, fileList) => {
  console.log(response);
  let { status, msg, data } = response;
  if (Number(status) >= 500) {
    exceptionMsg.value = msg;
    showErrorMsg.value = true;
  } else {
    if (msg?.length > 0) {
      ElMessage.info(msg);
    } else if (data && data == 'success') {
      ElMessage.success("导入成功");
    } else {
      ElMessage.error("导入失败");
    }
    emit("refresh");
  }
  uploader.value.clearFiles();
}
/**钩子函数： 进度*/
const onFileProgress = (event, file, fileList) => {
  percent.value = Number(event.percent.toFixed(2));
}
const beforeUpload = (file, fileList) => {
  showErrorMsg.value = false;
  errorMsgList.value.splice(0, errorMsgList.value.length);
  exceptionMsg.value = "";
}



</script>

<style lang="scss" scoped>
.asset-import {
  padding: 20px;

  :deep(.el-upload) {
    width: 100%;

    .el-upload-dragger {
      width: 100% !important;
      height: 250px;
    }

    .upload-desc {
      height: 18px;
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #6e7493;
      line-height: 18px;
      margin-top: 20px;
    }
  }

  .import-progress {
    margin-top: 20px;

    .complete-percent {
      font-size: 16px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: #3c4a54;
    }

    .filename {
      float: right;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: #3c4a54;
    }

    .upload-progress {
      font-size: 14px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #6e7493;
    }
  }

  .import-bz {
    margin-top: 8px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #3c4a54;
  }

  .error-items {
    .error-item {
      margin: 8px;
    }
  }
}
</style>
