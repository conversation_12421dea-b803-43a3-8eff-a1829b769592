<template>
  <el-drawer
    v-model="drawerVisible"
    :before-close="cancel"
    :show-close="true"
    :destroy-on-close="true"
    class="ai-event-analysis-drawer"
    size="60%"
    header-class="my-ai-header"
  >
    <template #header>
      <div class="flex-bc">
        <el-page-header @back="cancel">
          <template #content>
            <div class="flex justify-between">
              <span class="mr-3 font-bold"> AI智能研判 </span>
            </div>
          </template>
        </el-page-header>
        <div style="margin-right: 10px">
          <!--          <el-button v-if="!state.thinking" type="success" @click="callAi"-->
          <!--            >重新回答</el-button-->
          <!--          >-->
          <!--          <el-button-->
          <!--            v-if="state.thinking"-->
          <!--            type="danger"-->
          <!--            @click="stopReplayMessage"-->
          <!--            >停止回答</el-button-->
          <!--          >-->

          <el-button
            :disabled="state.thinking"
            type="primary"
            @click="handleDeal"
            >处置
          </el-button>
          <el-button
            :disabled="state.thinking"
            type="primary"
            @click="handleIpBlockHandler"
            >封堵
          </el-button>
          <el-button
            :disabled="state.thinking"
            type="primary"
            @click="batchFalsePositivesHandler"
            >误报
          </el-button>
        </div>
      </div>
    </template>
    <div class="ai-event-analysis-main">
      <div class="event-desc">
        <span class="desc"
          >由攻击IP {{ eventInfo.src_ip }} “{{ eventInfo.event_name }}”
          触发的安全事件</span
        >
        <!--        <el-tag type="danger">误报可能性低</el-tag>-->
        <!--        <el-tag type="danger">置信度90%</el-tag>-->
        <!--        <el-tag type="danger">风险等级高</el-tag>-->
      </div>
      <div ref="mailEl" class="ai-analysus-content-wrap">
        <div class="ai-analysus-content">
          <AiMarkdown
            :markdown-text="state.message"
            :show-ai-generate-desc="!state.thinking"
            ai-generate-desc="AI生成内容，仅供参考学习"
          >
            <template #extra="{ message }">
              <div
                v-if="!state.thinking"
                class="message-btn"
                style="
                  display: flex;
                  gap: 4px;
                  position: absolute;
                  right: 5px;
                  top: 4px;
                "
              >
                <el-button type="primary" link :icon="Refresh" @click="callAi">
                  重新回答
                </el-button>
                <el-button
                  type="primary"
                  link
                  :icon="CopyDocument"
                  @click="handleCopy(message)"
                >
                  复制
                </el-button>
              </div>
            </template>
          </AiMarkdown>
        </div>
      </div>
    </div>

    <!-- 处置 -->
    <event-deal-modal
      v-model:visible="deal.visible"
      :title="deal.title"
      :event-unit-ids="deal.unitIds"
      :default-action="deal.defaultAction"
      @deal-success="cancel"
    />

    <!-- 批量ip封堵 -->
    <multip-block-modal
      v-model:visible="ipBlock.multiVisible"
      title="IP批量封堵"
      :src-ips="ipBlock.srcIps"
      :dst-ips="ipBlock.dstIps"
      :event-uids="ipBlock.eventUids"
      @deal-success="cancel"
    ></multip-block-modal>
  </el-drawer>
</template>

<script lang="ts" setup>
import {
  getCurrentInstance,
  nextTick,
  onBeforeMount,
  reactive,
  ref,
  toRefs,
  watch
} from "vue";
import AiMarkdown from "@/views/modules/ai/components/AiMarkdown.vue";
import { getAiInfoByCode, ragChar } from "@/views/modules/ai/api";
import EventDealModal from "@/views/modules/security/event/components/EventDealModal.vue";
import MultipBlockModal from "@/views/modules/security/event/components/MultipBlockModal.vue";
import { CopyDocument, Refresh } from "@element-plus/icons-vue";
import { copyHtmlToStyleText } from "@/utils/copy";
import { ElMessage } from "element-plus";

const mailEl = ref<HTMLDivElement>();
const { $confirm } = getCurrentInstance().appContext.config.globalProperties;
//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  eventInfo: {
    type: Object,
    default: () => {}
  }
});

//数据对象
const state = reactive({
  drawerVisible: false,
  thinking: false,
  handleCancel: null,
  question: "",
  message: "",

  ak: null,

  deal: {
    visible: false,
    title: "",
    unitIds: [],
    defaultAction: null
  },

  ipBlock: {
    visible: false,
    ipAddress: "",
    defaultPlugLabel: "安全事件",

    // 批量封堵
    multiVisible: false,
    eventUids: [],
    srcIps: [],
    dstIps: []
  }
});
const { drawerVisible, deal, ipBlock } = toRefs(state);
const emit = defineEmits(["update:visible"]);

const handleDeal = () => {
  state.deal.title = "事件处置";
  state.deal.defaultAction = "manual";
  state.deal.unitIds = [props.eventInfo.event_uid];
  state.deal.visible = true;
};

const handleIpBlockHandler = () => {
  state.ipBlock.multiVisible = true;
  state.ipBlock.eventUids = [props.eventInfo.event_uid];
  state.ipBlock.srcIps = [props.eventInfo.src_ip];
  state.ipBlock.dstIps = [props.eventInfo.dst_ip];
};

const batchFalsePositivesHandler = () => {
  state.deal.title = "批量事件处置";
  // state.deal.defaultAction = "misreport";
  state.deal.defaultAction = "manual";
  state.deal.unitIds = [props.eventInfo.event_uid];
  state.deal.visible = true;
};

const cancel = () => {
  if (state.thinking) {
    $confirm("AI正在思考回答中，确定要关闭吗?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "info"
    })
      .then(() => {
        stopReplayMessage();
        emit("update:visible", false);
      })
      .catch(() => {});
  } else {
    emit("update:visible", false);
  }
};

const scrollToBottom = () => {
  nextTick(() => {
    if (mailEl.value) {
      let mainNode = mailEl.value as HTMLDivElement;
      mainNode.scrollTop = mainNode.scrollHeight;
    }
  });
};

const stopReplayMessage = async (onlyStop?) => {
  let cancel = state.handleCancel;
  if (cancel) {
    // 取消是异步操作
    cancel("用户手动终止");
  }
  state.thinking = false;
  if (!onlyStop) {
    nextTick(() => {
      scrollToBottom();
    });
  }
};

const finishReplayMessage = () => {
  state.thinking = false;
};

const callAi = async () => {
  // 一般在未思考模式下，如果正在思考先停止
  if (state.thinking || state.handleCancel) {
    stopReplayMessage(true);
  }
  state.message = "解读中...";
  // 构建axios取消句柄
  const cancelFunc = function executor(c) {
    // 保存取消函数
    state.handleCancel = c;
  };
  // 如果有取消的需要确保取消成功
  if (state.handleCancel) {
    // 存在请求未取消,取消调用后onDownloadProgress依然会继续执行, 如果放开会出现两个请求短暂同时消费后台的数据，导致回答内容乱串
    // 使用轮询判断上一个请求是否完成(取消或者正常完成)，这里暂时没有好的处理方案
    await new Promise(resolve => {
      let interval = setInterval(() => {
        if (!state.handleCancel) {
          resolve([]);
          clearInterval(interval);
        }
      }, 10);
    });
  }
  // 强制开始
  state.thinking = true;

  ragChar(
    state.ak,
    {
      event_uid: props.eventInfo.event_uid,
      src_ip: props.eventInfo.src_ip,
      dst_ip: props.eventInfo.dst_ip,
      event_name: props.eventInfo.event_name,
      last_time: props.eventInfo.last_time,
      src_org_id: props.eventInfo.src_org_id
    },
    ({ event }) => {
      // 处理异步取消延迟带来的问题
      const list = event.target.responseText.split("\n\n");
      let text = "";
      let isRun = true;
      list.forEach((i: any) => {
        if (i.startsWith("data:Error")) {
          isRun = false;
          text += i.substring(5, i.length);
          // chatStore.updateMessage(aiChatId.value, text, true);
          return;
        }
        if (!i.startsWith("data:{")) {
          return;
        }
        const dataStr = i.substring(5, i.length);
        try {
          const { done, message } = JSON.parse(dataStr);
          if (done || message === null) {
            isRun = false;
            finishReplayMessage();
            return;
          }
          text += message;
          scrollToBottom();
        } catch (e) {
          console.trace(e);
        }
      });
      if (text) {
        // end
        state.message = text;
      }
      if (!isRun) {
        scrollToBottom();
        return;
      }
    },
    cancelFunc
  )
    .catch(err => {
      if (err?.name === "CanceledError") {
        console.log("请求已取消");
      } else {
        state.message = "服务器繁忙，请稍后再试。";
      }
      return;
    })
    .finally(() => {
      // 请求完成或者取消完成
      state.handleCancel = null;
      finishReplayMessage();
    });

  nextTick(() => {
    scrollToBottom();
  });
};

const handleCopy = message => {
  let error = copyHtmlToStyleText(message);
  if (!error) {
    ElMessage.success("文本已复制到剪贴板！");
  } else {
    ElMessage.error("复制失败: " + error);
  }
};

watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
  callAi();
});

onBeforeMount(() => {
  getAiInfoByCode("security_event_judgement").then(res => {
    let resData = res.data;
    console.log("resData", resData);
    state.ak = resData.accessKey;
  });
});
</script>
<style lang="scss">
.ai-event-analysis-drawer {
  background: linear-gradient(101deg, #d6eaff 0%, #e8ecef 100%);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04);
  border-radius: 16px 16px 16px 16px;
}

.dark {
  .ai-event-analysis-drawer {
    background: #222020 !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0px 1px 2px 0px rgba(255, 255, 255, 0.4);
    color: rgba(255, 255, 255, 0.9) !important;
  }
}
</style>
<style lang="scss" scoped>
.ai-event-analysis-main {
  transform: translateY(-20px);

  .event-desc {
    display: flex;
    gap: 10px;

    .desc {
      opacity: 0.8;
    }
  }

  .ai-analysus-content-wrap {
    height: calc(100vh - 150px);
    overflow: auto;
    width: calc(100% + 10px);
    padding-right: 10px;
    .ai-analysus-content {
      margin-top: 10px;
      min-height: 500px;
    }
  }
}
</style>
