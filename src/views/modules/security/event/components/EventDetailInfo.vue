<template>
  <div>
    <div class="flex-bc">
      <el-page-header
        v-if="!hiddenJump"
        @back="jumpTo('eventDealManage')"
        class="mb-2"
      >
        <template #content>
          <span class="mr-3 font-bold"> 事件详情 </span>
        </template>
      </el-page-header>
      <div v-if="!readonly" class="grid-flow-row gap-2">
        <el-button
          type="primary"
          plain
          v-if="eventInfo.deal_status === '1'"
          @click="eventDealHandler"
          >处置
        </el-button>
      </div>
    </div>
    <div class="flex-sc items-start mt-3">
      <div class="ml-2 mr-2 w-full">
        <el-descriptions :column="2" direction="horizontal" border>
          <el-descriptions-item label="事件名称" :span="2" :width="130">
            {{ eventInfo.event_name }}
          </el-descriptions-item>
          <el-descriptions-item label="风险级别">
            <el-tag
              :color="getRiskLevelColor(eventInfo.reseverity)"
              class="text-white border-none"
            >
              {{ getRiskLevelLabel(eventInfo.reseverity) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="攻击阶段"
            >{{ eventInfo.attack_stage }}
          </el-descriptions-item>
          <el-descriptions-item label="攻击次数"
            >{{ eventInfo.attack_cnt }}
          </el-descriptions-item>
          <el-descriptions-item label="累计告警次数"
            >{{ eventInfo.event_cnt }}
          </el-descriptions-item>
          <el-descriptions-item label="首次发生时间"
            >{{ eventInfo.first_time }}
          </el-descriptions-item>
          <el-descriptions-item label="最近发生时间"
            >{{ eventInfo.last_time }}
          </el-descriptions-item>
          <el-descriptions-item label="派单状态"
            >{{ eventInfo.dispatch_status }}
          </el-descriptions-item>
          <el-descriptions-item label="处置状态">
            <el-tag>{{ getSegmentLabel(eventInfo.deal_status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="URL" :span="3">
            <div style="word-break: break-all; word-wrap: break-word">
              {{ eventInfo.key_url }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="事件描述" :span="3">
            <div style="word-break: break-all; word-wrap: break-word">
              {{ eventInfo.event_detail }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <el-divider direction="vertical" class="h-80" />
      <div class="flex-c w-full">
        <div class="w-[80%]">
          <div class="flex-c">
            <el-text class="w-64" style="text-align: center">
              {{ eventInfo.attack_type }}
            </el-text>
          </div>
          <div class="flex-c gap-5 h-6">
            <attackerImg class="w-20" />
            <rightArrow class="ml-4" />
            <victimImg class="w-20" />
          </div>
          <div class="flex-c">
            <el-text>
              攻击结果：{{ getAttackResult(eventInfo.attack_result) }}
            </el-text>
          </div>
          <div style="display: flex; gap: 20px; margin-top: 20px">
            <el-card shadow="always" style="width: 50%">
              <el-descriptions :column="1" direction="horizontal">
                <el-descriptions-item label="攻击者IP："
                  >{{ eventInfo.src_ip }}
                </el-descriptions-item>
                <el-descriptions-item label="MAC地址："
                  >{{ eventInfo.src_mac }}
                </el-descriptions-item>
                <el-descriptions-item label="端口："
                  >{{ eventInfo.src_port }}
                </el-descriptions-item>
                <el-descriptions-item label="资产名称："
                  >{{ eventInfo.src_asset_name }}
                </el-descriptions-item>
                <el-descriptions-item label="资产责任人："
                  >{{ eventInfo.src_contact_name }}
                </el-descriptions-item>
                <el-descriptions-item label="所在位置："
                  >{{ eventInfo.src_safe_area }}
                </el-descriptions-item>
                <el-descriptions-item label="IP归属："
                  >{{ eventInfo.src_country_name }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
            <el-card shadow="always" style="width: 50%">
              <el-descriptions :column="1" direction="horizontal">
                <el-descriptions-item label="受害者IP："
                  >{{ eventInfo.dst_ip }}
                </el-descriptions-item>
                <el-descriptions-item label="MAC地址："
                  >{{ eventInfo.dst_mac }}
                </el-descriptions-item>
                <el-descriptions-item label="端口："
                  >{{ eventInfo.dst_port }}
                </el-descriptions-item>
                <el-descriptions-item label="资产名称："
                  >{{ eventInfo.dst_asset_name }}
                </el-descriptions-item>
                <el-descriptions-item label="资产责任人："
                  >{{ eventInfo.dst_contact_name }}
                </el-descriptions-item>
                <el-descriptions-item label="所在位置："
                  >{{ eventInfo.dst_safe_area }}
                </el-descriptions-item>
                <el-descriptions-item label="IP归属："
                  >{{ eventInfo.dst_country_name }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </div>
        </div>
      </div>
    </div>
    <el-tabs
      v-model="activeTabName"
      class="demo-tabs"
      @tab-change="tabChangeHandler"
    >
      <el-tab-pane name="dealTab">
        <template #label>
          <div class="flex-c">
            <IconifyIconOffline icon="EP-Select" />
            <span class="ml-1">处置情况</span>
          </div>
        </template>
        <EventDealInfo :event-info="eventInfo"></EventDealInfo>
      </el-tab-pane>
      <el-tab-pane name="path">
        <template #label>
          <div class="flex-c">
            <IconifyIconOffline icon="RI-GitPullRequestFill" />
            <span class="ml-1">攻击路径</span>
          </div>
        </template>
        <attack-path
          :event-info="eventInfo"
          :src-ip="eventInfo.src_ip"
          :dst-ip="eventInfo.dst_ip"
        />
      </el-tab-pane>
      <el-tab-pane name="timeLine">
        <template #label>
          <div class="flex-c">
            <IconifyIconOffline icon="RI-TimerLine" />
            <span class="ml-1">关联告警时间线</span>
          </div>
        </template>
        <event-alarms-time-line :event-info="eventInfo" />
      </el-tab-pane>
      <el-tab-pane name="eventTrend">
        <template #label>
          <div class="flex-c">
            <IconifyIconOffline icon="RI-LineChartFill" />
            <span class="ml-1">趋势分析</span>
          </div>
        </template>
        <event-trend-detail ref="trendRef" :event-info="eventInfo" />
      </el-tab-pane>
      <el-tab-pane name="log">
        <template #label>
          <div class="flex-c">
            <IconifyIconOffline icon="EP-Memo" />
            <span class="ml-1">原始日志</span>
          </div>
        </template>
        <div style="position: relative">
          <monaco-editor
            ref="corroborationRef"
            v-model="state.raw_log"
            language="text"
            :height="contentHeight + 'px'"
            :options="{
              readOnly: true,
              wordWrap: 'on'
            }"
          />
          <div
            v-if="!!state.raw_log && enableAI"
            style="position: absolute; top: 10px; right: 20px; cursor: pointer"
            title="使用AI协助解读"
            @click="openAiDrawer"
          >
            <img width="32" height="32" :src="aiSvg" />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <event-deal-modal
      v-model:visible="deal.visible"
      :title="deal.title"
      :event-unit-ids="deal.unitIds"
      :default-action="deal.defaultAction"
    />

    <!-- ai 智能研判 -->
    <AILogAnalysisDrawer
      v-model:visible="aiContext.drawerVisible"
      :event-label="aiContext.eventLabel"
      :log="aiContext.log"
    ></AILogAnalysisDrawer>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, toRefs, watch } from "vue";
import attackerImg from "@/assets/security/u4209.svg?component";
import rightArrow from "@/assets/security/u4229.svg?component";
import victimImg from "@/assets/security/u4210.svg?component";
import AttackPath from "@/views/modules/security/event/components/AttackPath.vue";
import EventAlarmsTimeLine from "@/views/modules/security/event/components/EventAlarmsTimeLine.vue";
import {
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel
} from "@/views/modules/security/event/util/event_data";
import EventTrendDetail from "@/views/modules/security/event/components/EventTrendDetail.vue";
import EventDealModal from "@/views/modules/security/event/components/EventDealModal.vue";
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";
import EventDealInfo from "@/views/modules/security/event/components/EventDealInfo.vue";
import AILogAnalysisDrawer from "@/views/modules/security/event/components/AILogAnalysisDrawer.vue";
import aiSvg from "../assets/ai2.svg?url";
import { getConfig } from "@/config";
import { getAttackResult } from "@/views/modules/security/event/api/SecurityEventApi";

const trendRef = ref(null);

// 组件属性
const props = defineProps({
  // 是否只读
  readonly: Boolean,
  // 隐藏返回按钮
  hiddenJump: Boolean,
  eventInfo: {
    type: Object,
    default: () => {}
  }
});

const enableAI = computed(() => {
  return getConfig()?.EnableAI;
});

//数据对象
const state = reactive({
  deal: {
    visible: false,
    title: "",
    unitIds: [],
    defaultAction: null
  },
  activeTabName: "dealTab",
  raw_log: ""
});
const { deal, activeTabName } = toRefs(state);

//定义事件
const emit = defineEmits(["jump-to"]);

//标签变更触发
const tabChangeHandler = (name: string) => {
  if (name === "eventTrend") {
    trendRef.value.initData();
  }
};

//根据页面高度设置表格高度
const contentHeight = computed(() => {
  return document.documentElement.offsetHeight - 400;
});

//事件处置触发
const eventDealHandler = () => {
  state.deal.title = "事件处置";
  state.deal.defaultAction = "";
  state.deal.unitIds = [props.eventInfo.event_uid];
  state.deal.visible = true;
};

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};

const aiContext = reactive({
  log: null,
  eventLabel: "",
  drawerVisible: false
});

const openAiDrawer = () => {
  aiContext.log =
    state.raw_log +
    "；你是一个资深网络安全专家，请用解读上述安全日志内容及总结，将解读结果中重复字段去掉; 要求如下: 1.列出该安全日志的json格式，以中文字段显示字段、时间格式转换成UTC+8; 2.分析攻击模式；3.提供攻击特征；4.潜在风险分析；5.建议应对措施；6.总结上述内容。";

  const eventInfo = props.eventInfo;
  aiContext.eventLabel = `由攻击IP ${eventInfo.src_ip} “${eventInfo.event_name}” 触发的安全事件 原始日志解读: `;

  aiContext.drawerVisible = true;
};

watch(
  () => props.eventInfo,
  val => {
    state.raw_log = val?.raw_log || ``;
  },
  { immediate: true }
);
</script>
