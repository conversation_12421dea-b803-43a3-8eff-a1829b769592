<template>
  <div class="flex flex-col gap-4 pl-4">
    <div>
      <h5>佐证信息:</h5>
      <el-divider border-style="dashed"></el-divider>
      <template v-if="eventInfo">
        <div style="position: relative">
          <monaco-editor
            ref="corroborationRef"
            v-model="state.event_evidence"
            language="text"
            height="240px"
            :options="{
              readOnly: true
            }"
            fit-content-height
            :max-lines="20"
          />
          <div
            v-if="!!state.event_evidence&&enableAI"
            style="position: absolute; top: 10px; right: 20px; cursor: pointer"
            title="使用AI协助解读"
            @click="openAiDrawer"
          >
            <img width="32" height="32" :src="aiSvg"/>
          </div>
        </div>
      </template>
    </div>
    <div>
      <h5>事件危害说明:</h5>
      <el-divider border-style="dashed"></el-divider>
      <div class="descr">{{ event.damage_event_content || "暂无" }}</div>
    </div>
    <div>
      <h5>事件分析建议:</h5>
      <el-divider border-style="dashed"></el-divider>
      <div class="descr">{{ event.analyze_suggest || "暂无" }}</div>
    </div>
    <div>
      <h5>处置办法:</h5>
      <el-divider border-style="dashed"></el-divider>
      <div class="descr">{{ event.solution || "暂无" }}</div>
    </div>

    <div>
      <h5>处置说明:</h5>
      <el-divider border-style="dashed"></el-divider>
      <el-form>
        <el-row>
          <el-col :span="4">
            <el-form-item class="form-label" label="工单编号:">
              <div>{{ event.wo_no || "暂无" }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="form-label" label="是否发送通知:">
              <template v-if="hasNotice">
                <el-button v-if="event.smsForwardFlag" type="success"
                >短信通知
                </el-button
                >
                <el-button v-if="event.webchatForwardFlag" type="success"
                >微信通知
                </el-button
                >
              </template>
              <span>暂无</span>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item class="form-label" label="处置人:">
              <div>{{ event.deal_user_name || "-" }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item class="form-label" label="处置时间:">
              <div>{{ event.deal_time || "-" }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item class="form-label" label="处置方式:">
              <div>{{ dealMethods[event.deal_model] || "-" }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item class="form-label" label="处置说明:">
          <div>{{ event.deal_idea || "暂无" }}</div>
        </el-form-item>
      </el-form>
    </div>

    <!-- ai 智能研判 -->
    <AILogAnalysisDrawer
      v-model:visible="aiContext.drawerVisible"
      :event-label="aiContext.eventLabel"
      :log="aiContext.log"
    ></AILogAnalysisDrawer>
  </div>
</template>
<script lang="ts" setup>
import {computed, reactive, watch} from "vue";
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";
import AILogAnalysisDrawer from "@/views/modules/security/event/components/AILogAnalysisDrawer.vue";
import aiSvg from "../assets/ai2.svg?url";
import {getConfig} from "@/config";

const props = defineProps({
  eventInfo: Object
});

const event = computed(() => {
  return props.eventInfo || {};
});

const enableAI = computed(() => {
  return getConfig()?.EnableAI;
});

const state = reactive({
  event_evidence: ""
});

const dealMethods = reactive({
  workorder: "工单处置",
  manual: "人工处置",
  forbid: "封堵",
  misreport: "误报"
});

const hasNotice = computed(() => {
  return event.value.smsForwardFlag || event.value.webchatForwardFlag;
});

const lines = text => {
  if (!text) return 0;
  const lines = text.split(/\r?\n/);
  return lines.length - 1;
};

const aiContext = reactive({
  log: null,
  eventLabel: "",
  drawerVisible: false
});

const openAiDrawer = () => {
  aiContext.log =
    state.event_evidence +
    "；你是一个网络安全专家，请解读以上安全日志内容及总结，要求：1.分析攻击模式；2.提供攻击特征；3.潜在风险分析；4.建议应对措施；5.总结上述内容。";

  const eventInfo = props.eventInfo;
  aiContext.eventLabel = `由攻击IP ${eventInfo.src_ip} “${eventInfo.event_name}” 触发的安全事件 佐证信息解读: `;

  aiContext.drawerVisible = true;
};

watch(
  () => props.eventInfo,
  val => {
    state.event_evidence = val?.event_evidence || "暂无";
  },
  {immediate: true}
);
</script>
<style lang="scss" scoped>
.descr {
  font-size: 0.9em;
  opacity: 0.75;
}

.form-label {
  font-weight: 540;
  font-size: 14px;
}
</style>
