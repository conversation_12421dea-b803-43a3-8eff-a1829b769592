<template>
  <div>
    <!-- 使用splitpane组件，设置左右分割的比例 -->
    <splitpane :splitSet="settingLR">
      <!-- 左侧面板 -->
      <template #paneL>
        <!-- 使用el-tabs组件，设置默认激活的标签页 -->
        <el-tabs
          v-model="activeTabName"
          class="ml-2 mr-2"
          @tab-change="viewTabChangeHandler"
        >
          <!-- 组织视图标签页 -->
          <el-tab-pane name="deptView">
            <!-- 设置标签页的标题 -->
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-TeamFill" />
                <span class="ml-1">组织视图</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索组织 -->
              <div class="pb-1.5 flex items-center">
                <el-input
                  placeholder="组织名称"
                  :suffix-icon="useRenderIcon('EP-Search')"
                  clearable
                  v-model="deptKeyWord"
                />
                <el-icon
                  @click="reloadDeptTree"
                  style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  "
                >
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 树形控件，用于展示组织结构
              :load="loadNode"
                lazy
              -->
              <el-tree
                ref="deptTree"
                :data="state.deptData"
                :props="treeProps"
                node-key="deptId"
                :render-after-expand="false"
                :expand-on-click-node="false"
                highlight-current
                :default-expanded-keys="['', '0', '-1']"
                :filter-node-method="filterDeptNode"
                :style="treeStyle"
                @current-change="deptSelectChange"
              >
                <template #default="{ node, data }">
                  <span
                    :style="{
                      color: (data.eventCount || 0) > 0 ? 'red' : 'unset'
                    }"
                    >{{ node.label
                    }}<!--({{ data.eventCount || 0 }})--></span
                  >
                </template>
              </el-tree>
            </div>
          </el-tab-pane>
          <!-- 应用视图标签页 -->
          <el-tab-pane name="appView">
            <!-- 设置标签页的标题 -->
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-AppsFill" />
                <span class="ml-1">应用视图</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索应用 -->
              <div class="pb-1.5 flex items-center">
                <el-input
                  placeholder="应用名称"
                  :suffix-icon="useRenderIcon('EP-Search')"
                  clearable
                  v-model="appKeyWord"
                />
                <el-icon
                  @click="loadAppData"
                  style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  "
                >
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 树形控件，用于展示应用结构 -->
              <el-tree
                ref="appTreeRef"
                :data="appData"
                node-key="assetApp"
                default-expand-all
                :expand-on-click-node="false"
                :props="{
                  label: 'assetApp'
                }"
                highlight-current
                :filter-node-method="filterAppNode"
                :style="treeStyle"
                @current-change="appChangeHandler"
              >
                <template #default="{ node, data }">
                  <span
                    :style="{
                      color: (data.count || 0) > 0 ? 'red' : 'unset'
                    }"
                    >{{ node.label
                    }}<!--({{ data.count || 0 }})--></span
                  >
                </template>
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>
      <!-- 右侧面板 -->
      <template #paneR>
        <!-- 使用search-with-column组件，用于搜索事件数据 -->
        <search-with-column
          v-model="columnCondition.value"
          v-model:fuzzy-enable="columnCondition.fuzzy"
          v-model:column-val="columnCondition.field"
          :column-options="columnSearchOptions"
          :column-select-width="90"
          @search="resetTablePageAndQuery"
          @reset="resetSearchHandler"
          class="flex-c w-full"
          input-class-name="w-1/2"
        />
        <!-- 风险标签 -->
        <div class="flex mt-6 mb-6">
          <span
            style="
              width: 5em;
              opacity: 0.75;
              white-space: nowrap;
              margin-right: 10px;
              text-align: right;
            "
            >风险标签:</span
          >
          <div>
            <el-check-tag
              class="mr-2"
              :checked="state.checkAll"
              @change="tagChangeHandler(null)"
              >全部
            </el-check-tag>
            <el-check-tag
              v-for="item in eventTagData"
              :key="item.tagId"
              :checked="item.checked"
              class="mr-2"
              @change="tagChangeHandler(item)"
            >
              {{
                item.tagCount > 0
                  ? `${item.tagName}(${item.tagCount})`
                  : `${item.tagName}`
              }}
            </el-check-tag>
          </div>
        </div>
        <!-- 事件表格 -->
        <div class="ml-2 mr-2">
          <im-table
            ref="tableRef"
            :data="tableData"
            show-overflow-tooltip
            center
            toolbar
            table-alert
            :operator="{
              label: '操作',
              width: 210
            }"
            :height="tableOption.height"
            :stripe="tableOption.stripe"
            show-checkbox
            :pagination="tablePage"
            :loading="tableLoading"
            :column-storage="createColumnStorage('event', 'remote')"
            :filter-data-provider="filterDataProvider"
            @on-reload="resetTablePageAndQuery"
            @selection-change="selectionChangeHandler"
            @on-page-change="queryEventData"
          >
            <!-- 表格左侧菜单 -->
            <template #toolbar-left="{ size }">
              <div class="flex-sc pt-0.5">
                <!-- 分段选择器，用于选择事件处理状态 -->
                <el-segmented
                  v-model="searchCondition.dealWith"
                  :options="segmentData"
                  @change="segmentChangeHandler"
                ></el-segmented>
              </div>
            </template>
            <!-- 表格右侧菜单 -->
            <template #toolbar-right="{ size }">
              <div class="float-left flex-sc pr-3 pt-0.5 gap-3">
                <el-segmented
                  v-model="state.dateRangeSign"
                  :options="state.timeSegmentOptions"
                  @change="
                    () => {
                      dateTimeRange = [];
                      resetTablePageAndQuery();
                    }
                  "
                >
                </el-segmented>
                <!-- 日期选择器，用于选择事件时间范围 -->
                <el-date-picker
                  v-model="dateTimeRange"
                  type="daterange"
                  range-separator="到"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="true"
                  style="width: 200px"
                  @change="dateChangeFunction"
                />
                <!-- 数据来源 -->
                <el-select
                  v-model="searchCondition.event_agent"
                  placeholder="数据来源"
                  clearable
                  multiple
                  collapse-tags
                  style="width: 160px"
                  class="ml-3"
                  @change="resetTablePageAndQuery"
                >
                  <el-option
                    v-for="item in state.dsSelData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-select
                  v-model="searchCondition.dev_name"
                  placeholder="设备来源"
                  clearable
                  multiple
                  collapse-tags
                  style="width: 160px"
                  class="ml-3"
                  @change="resetTablePageAndQuery"
                >
                  <el-option
                    v-for="item in state.devNameSelData"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <!-- 风险级别选择器，用于选择事件风险级别 -->
                <el-select
                  v-model="searchCondition.reseverity"
                  placeholder="风险级别"
                  multiple
                  collapse-tags
                  clearable
                  style="width: 150px"
                  @change="riskLevelChangeHandler"
                >
                  <el-option
                    v-for="item in riskLevelData"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  />
                </el-select>
                <!-- 批量操作下拉菜单 -->
                <el-dropdown>
                  <span class="flex-sc text-primary font-bold">
                    批量操作
                    <IconifyIconOffline icon="EP-ArrowDown" />
                  </span>
                  <template #dropdown>
                    <div class="pt-2 pb-2">
                      <el-dropdown-menu>
                        <!-- 设置为误报 -->
                        <el-dropdown-item>
                          <el-link
                            plain
                            :underline="false"
                            :size="size"
                            type="primary"
                            :icon="useRenderIcon('RI-MailcloseFill')"
                            :disabled="selectedEvents.length === 0"
                            @click="batchFalsePositivesHandler"
                          >
                            批量处置
                          </el-link>
                        </el-dropdown-item>

                        <!-- 未处置下显示两个批量操作  -->
                        <template v-if="searchCondition.dealWith == '1'">
                          <!-- 批量封堵 -->
                          <el-dropdown-item>
                            <el-link
                              plain
                              :underline="false"
                              :size="size"
                              type="primary"
                              :disabled="unDealEvents.length == 0"
                              :icon="useRenderIcon('RI-MailcloseFill')"
                              @click="batchBlockIpList"
                            >
                              批量封堵
                            </el-link>
                          </el-dropdown-item>
                          <!-- 批量派单 -->
                          <el-dropdown-item>
                            <el-link
                              plain
                              :underline="false"
                              :size="size"
                              type="primary"
                              :icon="useRenderIcon('EP-List')"
                              :disabled="unDealEvents.length === 0"
                              @click="batchDispatchList"
                            >
                              批量派单
                            </el-link>
                          </el-dropdown-item>
                        </template>
                      </el-dropdown-menu>
                    </div>
                  </template>
                </el-dropdown>
                <!-- 导出事件数据 -->
                <el-tooltip
                  content="导出事件数据"
                  placement="top"
                  :open-delay="1000"
                >
                  <el-button
                    :icon="useRenderIcon('EP-Download')"
                    circle
                    :size="size"
                    :disabled="tableData.length == 0"
                    @click="exportEventHandler"
                  />
                </el-tooltip>
              </div>
            </template>
            <!-- 表格操作按钮 -->
            <template #operator="{ row, size, type }">
              <!-- ai研判 -->
              <el-button
                :size="size"
                :type="type"
                text
                v-if="row.deal_status === '1' && enableAI"
                :disabled="!row.event_name"
                @click="openEventAiDrawer(row)"
              >
                <template #icon>
                  <img :src="aiPng" width="16" height="16" />
                </template>
                智能研判
              </el-button>
              <!-- 处置按钮 -->
              <el-button
                :size="size"
                :type="type"
                text
                :icon="useRenderIcon('EP-Checked')"
                @click="eventDealHandler(row)"
                v-if="row.deal_status === '1'"
              >
                处置
              </el-button>
              <!-- 详情按钮 -->
              <el-button
                :size="size"
                :type="type"
                text
                :icon="useRenderIcon('EP-View')"
                @click="detailViewHandler(row)"
              >
                详情
              </el-button>
            </template>
            <!-- 风险级别列 -->
            <template #reseverity="{ row }">
              <el-tag
                :color="getRiskLevelColor(row.reseverity)"
                class="text-white border-none"
              >
                {{ getRiskLevelLabel(row.reseverity) }}
              </el-tag>
            </template>
            <!-- 事件处理状态列 -->
            <template #deal_status="{ row }">
              <el-tag effect="light" :type="getDealStatusType(row.deal_status)">
                {{ getSegmentLabel(row.deal_status) }}
              </el-tag>
            </template>
            <template #src_ip="{ row }">
              <!--              <el-text>{{ row.src_ip }}</el-text>-->
              <!--              <br></br>-->
              <el-popover
                placement="right-start"
                trigger="hover"
                width="270"
                @show="querySingleAsset(row.src_ip)"
              >
                <assets-popover-content
                  :key="getAssetInfo(row.src_ip).key"
                  :event-info="row"
                  :asset-info="getAssetInfo(row.src_ip).data"
                  direction="src"
                  @ip-block="ipBlockHandler"
                />
                <template #reference>
                  <el-text class="text-primary"
                    >{{ row.src_asset_name }}
                  </el-text>
                </template>
              </el-popover>
            </template>
            <!-- 目标IP列 -->
            <template #dst_ip="{ row }">
              <!--              <el-text>{{ row.dst_ip }}</el-text>-->
              <!--              <br></br>-->
              <el-popover placement="right-start" trigger="hover" width="270">
                <assets-popover-content
                  :key="getAssetInfo(row.dst_ip).key"
                  :event-info="row"
                  :asset-info="getAssetInfo(row.dst_ip).data"
                  direction="dst"
                  @ip-block="ipBlockHandler"
                />
                <template #reference>
                  <el-text class="text-primary"
                    >{{ row.dst_asset_name }}
                  </el-text>
                </template>
              </el-popover>
            </template>

            <template #event_subtype_name="{ row }">
              <el-tag>{{ row.event_subtype_name }}</el-tag>
            </template>

            <!-- 攻击结果 -->
            <template #attack_result="{ row }">
              <span>{{ row.attack_result_text }}</span>
            </template>
          </im-table>
        </div>
      </template>
    </splitpane>
    <event-deal-modal
      v-model:visible="deal.visible"
      :title="deal.title"
      :event-unit-ids="deal.unitIds"
      :default-action="deal.defaultAction"
      @deal-success="queryEventData"
    />
    <!-- 单个ip封堵 -->
    <ip-block-modal
      v-model:visible="ipBlock.visible"
      title="IP封堵"
      :ip-address="ipBlock.ipAddress"
      :default-plug-label="ipBlock.defaultPlugLabel"
    />

    <!-- 批量ip封堵 -->
    <multip-block-modal
      v-model:visible="ipBlock.multiVisible"
      title="IP批量封堵"
      :src-ips="ipBlock.srcIps"
      :dst-ips="ipBlock.dstIps"
      :event-uids="ipBlock.eventUids"
      @deal-success="queryEventData"
    ></multip-block-modal>

    <!-- 批量派单-->
    <event-dispatch-modal
      v-model="eventDispatchContext.dispatchVisible"
      :form="eventDispatchContext.dispatchForm"
      @success="resetTablePageAndQuery"
    ></event-dispatch-modal>

    <!-- ai 智能研判 -->
    <AIEventAnalysisDrawer
      v-model:visible="aiContext.drawerVisible"
      :event-info="aiContext.event"
    ></AIEventAnalysisDrawer>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  nextTick,
  onBeforeMount,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch
} from "vue";
import dayjs from "dayjs";
import { Refresh } from "@element-plus/icons-vue";
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import { useDeptStoreHook } from "@/store/modules/dept";
import { SimpleDeptInfo } from "@/views/system/deptmanage/api/DeptManageApi";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumn.vue";
import {
  getDealStatusType,
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel,
  riskLevelData,
  segmentData
} from "@/views/modules/security/event/util/event_data";
import {
  exportEventData,
  getAttackResult,
  getBusinessSystemData,
  getEventEnum,
  getEventList,
  getTagData,
  queryAssetsByIps,
  queryEventDeptTree,
  queryEventTableHeadGroup,
  toEventAlarmRows
} from "@/views/modules/security/event/api/SecurityEventApi";
import AssetsPopoverContent from "@/views/modules/security/event/components/AssetsPopoverContent.vue";
import EventDealModal from "@/views/modules/security/event/components/EventDealModal.vue";
import IpBlockModal from "@/views/modules/security/event/components/IpBlockModal.vue";
import EventDispatchModal from "@/views/modules/security/event/components/EventDispatchModal.vue";
import MultipBlockModal from "@/views/modules/security/event/components/MultipBlockModal.vue";
import { useRoute } from "vue-router";
import AIEventAnalysisDrawer from "@/views/modules/security/event/components/AIEventAnalysisDrawer.vue";
import aiPng from "../assets/ai.png";
import { getConfig } from "@/config";
import {
  createColumnStorage,
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";

const route = useRoute();
const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const deptTree = ref<InstanceType<typeof ElTree>>();
const appTreeRef = ref<InstanceType<typeof ElTree>>();
const tableRef = ref<ImTableInstance>();

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const treeProps = {
  parentId: "parentId",
  label: "deptName",
  children: "children",
  isLeaf: "leaf"
};

const enableAI = computed(() => {
  return getConfig()?.EnableAI;
});

/**
 * 解析首层组织数据
 * @param firstLevelData 组织数据
 */
const parseFirstLevelDept = (firstLevelData: Array<SimpleDeptInfo>) => {
  if (deptTree.value) {
    nextTick(() => {
      deptTree.value!.setCurrentKey("-1");
    });
  }
  return [
    {
      deptId: "-1",
      deptName: "全部组织",
      children: firstLevelData,
      leaf: false
    }
  ];
};

const loadEventEnum = async (stateKey, field: string) => {
  const res = await getEventEnum(field);
  if (res.data && res.data.length > 0) {
    const dataArray = [];
    res.data.forEach((item: string) =>
      dataArray.push({
        label: item,
        value: item
      })
    );
    state[stateKey] = dataArray;
  }
};

// 数据来源
loadEventEnum("dsSelData", "event_agent");
//设备来源
loadEventEnum("devNameSelData", "dev_name");

const reloadDeptTree = () => {
  queryDeptTree();
};

const queryDeptTree = () => {
  // 清空关键字
  state.deptKeyWord = null;
  queryEventDeptTree().then(res => {
    // console.log("queryEventDeptTree", res.data);
    const deptData = Array.isArray(res.data) ? res.data : [res.data || {}];
    // 追加全部
    state.deptData = [
      {
        deptId: "",
        deptName: "全部",
        children: deptData
      }
    ];
    deptSelectChange(null);
  });
};

//首层组织数据 计算属性
// const firstLevelDeptData = computed(() => {
//   return parseFirstLevelDept(
//     useDeptStoreHook()?.deptData.filter(
//       (d: SimpleDeptInfo) => d.parentId === "-1"
//     )
//   );
// });

//数据对象
const state = reactive({
  checkAll: true,
  totalTagCount: 0,
  activeTabName: "deptView",
  tableLoading: false,
  deptKeyWord: null,
  // deptData: firstLevelDeptData,
  deptData: [],
  appKeyWord: null,
  appData: [],
  eventTagData: [],
  columnCondition: {
    value: null,
    field: "event_name",
    fuzzy: true,
    operator: "fuzzy"
  },
  dateRangeSign: "1d",
  timeSegmentOptions: [
    {
      label: "全部",
      value: ""
    },
    {
      label: "近24小时",
      value: "1d"
    },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    },
    {
      label: "近6月",
      value: "6m"
    },
    {
      label: "近1年",
      value: "1y"
    }
  ],
  dateTimeRange: [],
  searchCondition: {
    orgId: "",
    asset_app_name: "",
    dealWith: "1",
    event_agent: null,
    reseverity: [],
    event_type_tag: null,
    model: "event",
    dev_name:null
  },
  columnSearchOptions: [
    {
      label: "事件名称",
      value: "event_name"
    },
    {
      label: "攻击者IP",
      value: "src_ip"
    },
    {
      label: "受害者IP",
      value: "dst_ip"
    },
    {
      label: "攻击结果",
      value: "attack_result"
    }
  ],
  tableData: [],
  // 资产信息(以ip为key)
  assetByIpMap: {},
  tablePage: {
    hideOnEmptyData: true,
    total: 0,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  deal: {
    visible: false,
    title: "",
    unitIds: [],
    defaultAction: null
  },
  selectedEvents: [] as Array<string>,
  selectedEventRows: [],
  ipBlock: {
    visible: false,
    ipAddress: "",
    defaultPlugLabel: "安全事件",

    // 批量封堵
    multiVisible: false,
    eventUids: [],
    srcIps: [],
    dstIps: []
  },
  dsSelData: [],
  devNameSelData: [],
  filters: []
});
const {
  activeTabName,
  tableLoading,
  deptKeyWord,
  deptData,
  appKeyWord,
  appData,
  eventTagData,
  columnCondition,
  dateTimeRange,
  searchCondition,
  columnSearchOptions,
  tableData,
  tablePage,
  deal,
  selectedEvents,
  ipBlock
} = toRefs(state);

const eventDispatchContext = reactive({
  dispatchVisible: false,
  dispatchForm: {
    alertIds: "",
    userId: [],
    title: "",
    deptId: ""
  }
});

const unDealEvents = computed(() => {
  return state.selectedEventRows.filter(
    selectedEvent =>
      selectedEvent["deal_status"] == "1" && !selectedEvent["wo_id"]
  );
});

watch(deptKeyWord, val => {
  deptTree.value!.filter(val);
});

watch(appKeyWord, val => {
  appTreeRef.value!.filter(val);
});

//定义事件
const emit = defineEmits(["jump-to", "event-select"]);

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 360;
});

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 210,
  height: tableHeight,
  rowKey: "event_uid",
  column: [
    {
      label: "最近发生时间",
      prop: "last_time",
      sortable: true,
      width: 160
    },
    {
      label: "风险级别",
      prop: "reseverity",
      sortable: true,
      width: 120
    },
    {
      label: "事件类型",
      prop: "event_subtype_name",
      sortable: true,
      width: 120
    },
    {
      label: "事件名称",
      prop: "event_name",
      showOverflowTooltip: true,
      filters: true
    },
    {
      label: "攻击结果",
      prop: "attack_result_text"
    },
    {
      label: "攻击者IP",
      prop: "src_ip",
      filters: true
    },
    {
      label: "攻击端口",
      prop: "src_port",
      width: 80
    },
    {
      label: "受害者IP",
      prop: "dst_ip",
      filters: true
    },
    {
      label: "受害端口",
      prop: "dst_port",
      width: 80
    },
    {
      label: "处置状态",
      prop: "deal_status",
      sortable: true,
      width: 120
    }
  ]
});

const treeStyle = computed((): CSSProperties => {
  return {
    height: tableHeight.value + 80 + "px",
    overflow: "auto"
  };
});

//加载组织树节点数据
const loadNode = (node, resolve) => {
  if (node.isLeaf) return resolve([]);
  resolve(useDeptStoreHook().getChildren(node.data.deptId));
};

//组织数据搜索
const filterDeptNode = (value: string, data: SimpleDeptInfo) => {
  if (!value) return true;

  // 解决组件bug,根节点如果匹配失败可能出现过滤为空
  if (!data.deptId) return true;

  return data.deptName.indexOf(value) > -1;
};

//应用数据搜索
const filterAppNode = (value: string, data: any) => {
  if (!value) return true;

  if (data.assetApp == "全部") return true;

  return data.assetApp.indexOf(value) > -1;
};

//组织树选择改变触发
const deptSelectChange = (data: any) => {
  state.searchCondition.orgId = data ? data.deptId : null;
  if (!data) {
    deptTree.value.setCurrentKey(null);
  }
  resetTablePageAndQuery();
};

//应用选择改变
const appChangeHandler = (data: any) => {
  if (!data) {
    appTreeRef.value.setCurrentKey(null);
  }
  state.searchCondition.asset_app_name = data?.assetApp;
  // 查询全部时清空
  if (state.searchCondition.asset_app_name == "全部") {
    state.searchCondition.asset_app_name = "";
  }
  resetTablePageAndQuery();
};

//视图标签改变触发
const viewTabChangeHandler = (activeName: string) => {
  if (activeName === "deptView") {
    //处理组织视图初始化
    state.searchCondition.asset_app_name = "";
    nextTick(() => {
      if ("-1" == deptTree.value!.getCurrentKey()) {
        state.searchCondition.orgId = "-1";
        resetTablePageAndQuery();
      }
      deptTree.value!.setCurrentKey("-1");
    });
  } else if (activeName === "appView") {
    //处理应用视图初始化
    state.searchCondition.orgId = "";
    if (state.appData && state.appData.length > 0) {
      const firstData = state.appData[0];
      nextTick(() => {
        if (firstData.assetApp == appTreeRef.value!.getCurrentKey()) {
          state.searchCondition.asset_app_name = firstData.assetApp;
          resetTablePageAndQuery();
        }
        appTreeRef.value!.setCurrentKey(firstData.assetApp);
      });
    }
  }
};

//初始化默认时间范围
// const initTimeRange = () => {
//   state.dateTimeRange = [
//     dayjs().add(-30, "d").startOf("day"),
//     dayjs().endOf("day")
//   ];
// };

//加载业务系统数据
const loadAppData = async () => {
  const appRes = await getBusinessSystemData();
  const appData = Array.isArray(appRes.data) ? appRes.data : [];
  state.appData = [
    {
      assetApp: "全部",
      children: appData
    }
  ];
  appChangeHandler(null);
};
loadAppData();

//重置分页后查询事件数据
const resetTablePageAndQuery = (keepFiltersFlag?: boolean) => {
  if (keepFiltersFlag !== true) {
    // 只要没有声明keepFilters = true，清除表格所有过滤器, 只有在on-filter中会指定keepFilters为true
    state.filters = [];
    tableRef.value.clearAllFilters();
  }
  state.tablePage.currentPage = 1;
  queryEventData();
};

/**
 * 日期范围(标签和列表能复用)
 */
const computedDateRange = computed(() => {
  let dateRange;
  if (state.dateRangeSign) {
    dateRange = state.dateRangeSign;
  } else {
    if (state.dateTimeRange?.length == 2) {
      dateRange = [
        dayjs(state.dateTimeRange[0]).format("YYYY-MM-DD HH:mm:ss"),
        dayjs(state.dateTimeRange[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
      ];
    } else {
      dateRange = null;
    }
  }
  return dateRange;
});

/**
 * 复用列表查询条件
 */
const buildQueryCondition = (isQueryPage?: boolean) => {
  console.log("getEventListConditions filters", state.filters);
  let query = {
    //查询条件
    conditions: state.columnCondition.value ? [state.columnCondition] : [],
    //日期范围
    dateRange: computedDateRange.value,
    //搜索条件
    ...state.searchCondition,
    headerFilter: {
      filters: state.filters
    }
  };

  if (isQueryPage) {
    Object.assign(query, {
      //当前页码
      pageNum: state.tablePage.currentPage,
      //每页显示条数
      pageSize: state.tablePage.pageSize
    });
  }
  return query;
};

//查询事件数据
const queryEventData = async () => {
  //设置表格加载状态为true
  state.tableLoading = true;
  state.tableData = [];
  state.tablePage.total = 0;
  dealFuzzEnable();
  try {
    //根据条件查询事件列表
    const res = await getEventList(buildQueryCondition(true));
    //设置表格数据
    state.tableData = toEventAlarmRows(res.data.rows || []);
    // 查询关联的资产
    queryAssetData(state.tableData);
    //设置表格总条数
    state.tablePage.total = res.data.totalElements;
    //加载标签数据
    await loadEventTagData();
  } catch (e) {
    console.error(e);
  }
  //设置表格加载状态为false
  state.tableLoading = false;
};

const dateChangeFunction = query => {
  state.dateRangeSign = null;
  if (!query) {
    state.dateRangeSign = "30d";
  }
  resetTablePageAndQuery();
};

const queryAssetData = rows => {
  let ipList = [];
  for (let row of rows) {
    let { src_ip, dst_ip } = row;
    if (src_ip && !ipList.includes(src_ip)) {
      ipList.push(src_ip);
      state.assetByIpMap[src_ip] = {
        key: 1
      };
    }
    if (dst_ip && !ipList.includes(dst_ip)) {
      ipList.push(dst_ip);
      state.assetByIpMap[dst_ip] = {
        key: 1
      };
    }
  }
  state.assetByIpMap = {};
  queryAssetsByIps(ipList.join(","))
    .then(res => {
      let assets = res.data || [];
      for (let asset of assets) {
        // console.log("asset ", asset);
        // state.assetByIpMap
        let { ipAddress } = asset;
        state.assetByIpMap[ipAddress] = {
          key: ipAddress,
          data: asset
        };
      }
    })
    .finally(() => {});
};

const getAssetInfo = ip => {
  let assetInfo = state.assetByIpMap[ip];
  return (
    assetInfo || {
      key: 1
    }
  );
};

// 查询当个ip绑定的资产（防止重复查询）
const querySingleAsset = ipAddress => {
  let assetInfo = state.assetByIpMap[ipAddress];
  if (assetInfo && assetInfo.data) {
    return;
  }
  // 防止重复查询
  state.assetByIpMap[ipAddress] = {
    key: 1,
    data: {}
  };
  queryAssetsByIps(ipAddress)
    .then(res => {
      let assets = res.data || [];
      if (assets.length > 0) {
        state.assetByIpMap[ipAddress] = {
          key: ipAddress,
          data: assets[0]
        };
      }
    })
    .finally(() => {});
};

//加载标签数据
const loadEventTagData = async () => {
  dealFuzzEnable();
  //构建查询条件
  const condition = buildQueryCondition(false);
  condition.event_type_tag = "";

  //查询数据
  const tagRes = await getTagData(condition);
  const resData = tagRes.data || [];

  //处理标签已选中状态
  if (state.searchCondition.event_type_tag) {
    //查找已选中的标签
    const matchData = resData.find(
      (item: any) => item.tagId === state.searchCondition.event_type_tag.tagId
    );
    //设置标签已选中状态
    if (matchData) {
      matchData.checked = true;
    }
  }
  //设置标签数据
  state.eventTagData = resData;
  // 全部数量
  state.totalTagCount =
    resData.length == 0
      ? 0
      : resData
          .map(r => r?.tagCount || 0)
          .reduce((c1, c2) => {
            return c1 + c2;
          });
};

//处理模糊标识
function dealFuzzEnable() {
  if (state.columnCondition.fuzzy) {
    state.columnCondition.operator = "fuzzy";
  } else {
    state.columnCondition.operator = "exact";
  }
}

//风险标签选中改变
const tagChangeHandler = (tag: any) => {
  state.searchCondition.event_type_tag = tag;
  state.checkAll = !tag;
  state.eventTagData.forEach((item: any) => {
    item.checked = item.tagId === tag?.tagId;
  });
  resetTablePageAndQuery();
};

//处置状态改变
const segmentChangeHandler = () => resetTablePageAndQuery();

//日期范围改变触发
const dateRangeChangeHandler = (range: Array<Date>) => {
  resetTablePageAndQuery();
};

//风险级别改变触发
const riskLevelChangeHandler = (level: string) => {
  resetTablePageAndQuery();
};

//重置查询条件
const resetSearchHandler = () => {
  //重置列搜索
  state.columnCondition = {
    value: null,
    field: "event_name",
    fuzzy: true,
    operator: "fuzzy"
  };
  //重置标签选择状态
  state.eventTagData.forEach((item: any) => (item.checked = false));
  state.searchCondition.event_type_tag = null;
  // 重置标签选择全部
  state.checkAll = true;
  //重置处置状态
  state.searchCondition.dealWith = "1";
  //重置时间范围
  state.dateTimeRange = [];
  state.dateRangeSign = "1d";
  //重置风险级别
  state.searchCondition.reseverity = [];
  // 重置数据来源
  state.searchCondition.event_agent = [];
  // 重置标签选择全部

  resetTablePageAndQuery();
};

//查看事件详情触发
const detailViewHandler = (evt: any) => {
  emit("event-select", evt);
  jumpTo("eventDetailInfo");
};

//事件处置触发-单条
const eventDealHandler = (evt: any) => {
  state.deal.title = "事件处置";
  // state.deal.defaultAction = "misreport";
  state.deal.defaultAction = "manual";
  state.deal.unitIds = [evt.event_uid];
  state.deal.visible = true;
};

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.event_uid));
  state.selectedEvents = selectIds;
  state.selectedEventRows = selRows;
};

//批量误报触发
const batchFalsePositivesHandler = () => {
  state.deal.title = "批量事件处置";
  // state.deal.defaultAction = "misreport";
  state.deal.defaultAction = "manual";
  state.deal.unitIds = state.selectedEvents;
  state.deal.visible = true;
};

// 批量派单
const batchDispatchList = () => {
  eventDispatchContext.dispatchVisible = true;
  eventDispatchContext.dispatchForm = {
    userId: [],
    title: "",
    deptId: "",
    alertIds: unDealEvents.value
      .filter(unDealEvent => !!unDealEvent.event_uid)
      .map(unDealEvent => unDealEvent.event_uid)
      .join(",")
  };
};

//导出事件数据触发
const exportEventHandler = () => {
  $confirm(`您确认要导出当前查询条件下的事件数据么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    $message({
      message: "数据正在导出中...",
      type: "success"
    });
    await exportEventData({
      //查询条件
      conditions: state.columnCondition.value ? [state.columnCondition] : [],
      //日期范围
      dateRange: computedDateRange.value,
      //搜索条件
      ...state.searchCondition
    });
  });
};

//IP地址封堵触发
const ipBlockHandler = (ip: string) => {
  state.ipBlock.ipAddress = ip;
  state.ipBlock.visible = true;
};

const batchBlockIpList = () => {
  state.ipBlock.multiVisible = true;
  state.ipBlock.eventUids = state.selectedEvents;
  state.ipBlock.srcIps = unDealEvents.value.map(event => event.src_ip);
  state.ipBlock.dstIps = unDealEvents.value.map(event => event.dst_ip);
};

const filterDataProvider: TableFilterDataProvider = {
  options: (prop, filters: HeaderFilterValue[]) => {
    return new Promise<{ total: number; options: FilterOption[] }>(resolve => {
      const query = buildQueryCondition() as any;
      // 覆盖headerFilter
      query["headerFilter"] = {
        prop,
        filters
      };
      queryEventTableHeadGroup(query)
        .then(res => {
          console.log(res);
          resolve(res.data);
        })
        .catch(err => {
          console.error(err);
          resolve({ total: 0, options: [] });
        });
    });
  },
  // 转化攻击结果
  getOptionLabel: (prop, label, option) => {
    if (prop == "attack_result") {
      return getAttackResult(label);
    }
    return label;
  },
  onFilter: (filters: HeaderFilterValue[]) => {
    state.filters = filters;
    resetTablePageAndQuery(true);
  }
};

const aiContext = reactive({
  event: null,
  drawerVisible: false
});

const openEventAiDrawer = event => {
  aiContext.event = event;
  aiContext.drawerVisible = true;
};

//挂载后初始化
onMounted(() => {
  // initTimeRange();
  // 查询事件组织树
  queryDeptTree();
  // queryEventData();
});

onBeforeMount(() => {
  // 接收路由传递的参数，这里简单处理下
  state.dateRangeSign = (route.query.dateRange ||
    route.query.dateRangeSign ||
    "1d") as string;
  if (route?.query?.dateRange || route?.query?.dateRangeSign) {
    // 默认选中全部
    state.searchCondition.dealWith = "0";
  }
});

watch(
  () => (route?.query?.dateRangeSign || route?.query?.dateRange) as string,
  val => {
    state.dateRangeSign = val || "1d";
    if (route?.query?.dateRange || route?.query?.dateRangeSign) {
      // 默认选中全部
      state.searchCondition.dealWith = "0";
    }
  },
  { immediate: true }
);

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
</script>
