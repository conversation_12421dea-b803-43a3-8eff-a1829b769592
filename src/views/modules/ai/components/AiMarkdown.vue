<template>
  <div class="ai-markdown content markdown-body">
    <div class="leading-relaxed break-words">
      <div v-html="state.message"></div>
    </div>
    <slot name="extra" v-bind="{ message: state.message }"></slot>
    <div v-if="showAiGenerateDesc && aiGenerateDesc" class="ai-generate-desc">
      <span>
        {{ aiGenerateDesc }}
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import mdKatex from "@traptitech/markdown-it-katex";
import mila from "markdown-it-link-attributes";

function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">复制</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`;
}

const mdi = new MarkdownIt({
  html: false,
  linkify: true,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language));
    if (validLang) {
      const lang = language ?? "";
      return highlightBlock(
        hljs.highlight(code, { language: lang }).value,
        lang
      );
    }
    return highlightBlock(hljs.highlightAuto(code).value, "");
  }
});

mdi.use(mila, { attrs: { target: "_blank", rel: "noopener" } });
mdi.use(mdKatex, {
  blockClass: "katexmath-block rounded-md p-[10px]",
  errorColor: " #cc0000"
});

const markdownToHtml = text => {
  try {
    return mdi.render(text);
  } catch (e) {
    return text;
  }
};

const props = defineProps({
  markdownText: String,
  showAiGenerateDesc: Boolean,
  aiGenerateDesc: String
});
const state = reactive({
  message: ""
});

watch(
  () => props.markdownText,
  val => {
    state.message = markdownToHtml(val || "");
  },
  {
    immediate: true
  }
);
</script>

<style scoped lang="scss">
@import "../styles";
.ai-markdown {
  :deep(table) {
    /** 解决table超出边界问题 */
    width: unset !important;
  }
  :deep(> div) {
    max-width: 100%;
  }
  background-color: var(--color-canvas-default);
  border-radius: 8px;
  position: relative;
  max-width: 100%;
  min-width: 0;
  padding: 10px 16px 20px 16px;
  .message-btn {
    position: absolute;
    bottom: 8px;
    display: none;
  }
  &:hover {
    .message-btn {
      display: block;
    }
  }
  .ai-generate-desc {
    position: absolute;
    top: calc(100% + 10px);
    text-align: center;
    opacity: 0.4;
  }
}
</style>
