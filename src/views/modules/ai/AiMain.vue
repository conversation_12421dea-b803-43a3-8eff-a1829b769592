<template>
  <el-tooltip
    content="我是SOP小助手, 可以点我回答问题"
    placement="left"
    :disabled="state.dialogVisible"
  >
    <span
      ref="aiHandleEl"
      class="ai-robot fixed right-[10px]"
      :style="handleStyle"
      draggable="true"
      @dragstart="handleDragStart"
      @dragend="handleDragEnd"
      @drag="handleDrag"
      @click="openOrQuit"
    >
      <img
        class="ai-robot-animate"
        :class="{
          'ai-robot-animateless': state.dialogVisible && state.handleAnimate
        }"
        src="./assets/robot.png"
        @dblclick="
          e => {
            e.stopPropagation();
            state.handleAnimate = !state.handleAnimate;
          }
        "
      />
    </span>
  </el-tooltip>

  <div
    ref="aiDialogEl"
    class="ai-dialog"
    :class="{
      'ai-dialog-visible': state.dialogVisible,
      'ai-dialog-fullscreen': state.isFullScreen
    }"
  >
    <div class="w-full h-full">
      <div class="ai-header">
        <div class="ai-title">
          <img class="title-img" src="./assets/title.png" />
          <span class="text">I-SOP 智能小助手</span>
        </div>
        <div class="ai-toolbar">
          <!--          <img-->
          <!--            class="img-item"-->
          <!--            src="./assets/quit.png"-->
          <!--            @click="quit()"-->
          <!--            title="退出"-->
          <!--          />-->
          <span class="assistant-name">{{ state.selectAiAssistant.name }}</span>
          <el-icon
            class="img-item"
            @click="state.isFullScreen = !state.isFullScreen"
            ><Rank
          /></el-icon>
          <el-popover
            placement="bottom"
            :teleported="false"
            :width="275"
            trigger="click"
            popper-class="robots-popper"
            :disabled="state.thinking"
          >
            <template #reference>
              <!--              <img-->
              <!--                class="assistant-more-img img-item"-->
              <!--                src="./assets/more-expand.png"-->
              <!--              />-->
              <el-icon
                class="img-item"
                :title="state.thinking ? '正在思考回复,禁止操作' : '更多智能体'"
                ><MoreFilled
              /></el-icon>
            </template>
            <div class="robot-items">
              <div
                class="robot-item"
                v-for="(item, index) in state.aiAssistants"
                :class="{
                  selected: index == state.selectAssistantIndex,
                  disabled: state.thinking
                }"
                :key="index"
                @click="handleSelectAiAssistant(item, index)"
              >
                <div class="robot-img-wrap">
                  <img
                    v-show="item == state.selectAiAssistant"
                    class="robot-img"
                    src="./assets/op-robot.png"
                  />
                  <img
                    v-show="item != state.selectAiAssistant"
                    class="robot-img"
                    src="./assets/op-robot-gray.png"
                  />
                </div>
                <div class="assistant-info">
                  <span class="assistant-name">{{ item.name }}</span>
                  <span class="assistant-mark">{{ item.mark }}</span>
                </div>
              </div>
            </div>
          </el-popover>
          <el-icon class="img-item" @click="quit" title="退出">
            <CloseBold></CloseBold>
          </el-icon>
        </div>
      </div>
      <!--      <div class="assistant-more">-->
      <!--        <span class="more-text">更多智能体</span>-->
      <!--      </div>-->
      <div class="ai-main">
        <div class="left">
          <div
            class="add-session"
            :class="{ disabled: state.thinking }"
            @click="openNewSession"
          >
            <el-icon class="icon"><CirclePlusFilled /></el-icon>
            <span class="text">新建对话</span>
          </div>
          <el-divider style="margin: 20px 0 !important"></el-divider>
          <div
            style="
              height: calc(100% - 60px);
              display: flex;
              flex-direction: column;
            "
          >
            <div class="historical-records" style="height: calc(49% - 21px)">
              <div class="list-title">
                <img src="./assets/his.png" />
                <span>历史记录</span>
              </div>
              <div
                class="record-list"
                style="height: calc(100% - 40px); overflow: auto"
              >
                <div
                  v-for="(historicalrecord, index) in state.historicalrecords"
                  class="record-list-item"
                  :class="{
                    selected: historicalrecord.id == state.conversationId
                  }"
                  @click="toHistoricalrecord(historicalrecord)"
                  :title="historicalrecord.question"
                  :key="index"
                >
                  {{ historicalrecord.question }}
                </div>

                <div
                  class="empty-list"
                  v-if="state.historicalrecords.length == 0"
                >
                  暂无记录
                </div>
              </div>
            </div>
            <el-divider style="margin: 20px 0 !important"></el-divider>
            <div
              class="frequently-asked-questions"
              style="height: calc(51% - 21px)"
            >
              <div class="list-title">
                <img src="./assets/faq.png" />
                <span>常见问题</span>
              </div>
              <div
                class="record-list"
                style="height: calc(100% - 40px); overflow: auto"
              >
                <div
                  v-for="(
                    frequentlyAskedQuestion, index
                  ) in state.frequentlyAskedQuestions"
                  class="record-list-item"
                  :title="frequentlyAskedQuestion.question"
                  :key="index"
                  @click="sendMessage(frequentlyAskedQuestion.question)"
                >
                  {{ frequentlyAskedQuestion.question }}
                </div>

                <div
                  class="empty-list"
                  v-if="state.frequentlyAskedQuestions.length == 0"
                >
                  暂无问题
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="center">
          <div ref="chatMessagesWrapEl" class="chat-messages">
            <div
              v-if="displayChatMessages.length == 0"
              class="chat-tip markdown-body"
            >
              <div class="tip">Hi，我是SOP智能小助手</div>
              <div class="sub-tip">
                作为SOP智能小助手，您可以向我提出SOP的使用问题。
              </div>
              <div class="suggest">
                <div class="label">您是否想了解：</div>
                <div class="btn">
                  <el-button type="primary" link @click="queryQuestionTop3"
                    >换一换</el-button
                  >
                </div>
              </div>
              <div class="suggest-lists">
                <div
                  class="suggest-list"
                  v-for="(item, index) in state.suggestList"
                  :key="index"
                  @click="sendMessage(item.question)"
                >
                  <div class="suggest-row">
                    <img src="./assets/msg.png" />
                    <span>{{ item.question }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="chat-message"
              v-for="(chatMessage, index) in displayChatMessages"
              :key="index"
            >
              <!-- 发送-->
              <div
                v-if="chatMessage.role == 'user'"
                class="message message-user"
              >
                <div class="avatar user-avatar" style="margin-left: 8px">
                  <img
                    src="./assets/admin.png"
                    style="width: 32px; height: 32px; border-radius: 16px"
                  />
                </div>
                <div class="content-wrap">
                  <div class="content">
                    <div v-html="chatMessage.message"></div>
                  </div>
                </div>
              </div>
              <!-- 接收-->
              <div v-else class="message message-assistant">
                <div class="avatar assistant-avatar" style="margin-right: 8px">
                  <img
                    src="./assets/assiistant.png"
                    style="width: 32px; height: 32px; border-radius: 16px"
                  />
                </div>
                <div class="content-wrap">
                  <div class="content markdown-body">
                    <div class="leading-relaxed break-words">
                      <div v-html="chatMessage.message"></div>
                    </div>
                    <div
                      v-if="!state.thinking"
                      class="message-btn"
                      style="display: flex; gap: 4px"
                    >
                      <el-button
                        type="primary"
                        link
                        :icon="Refresh"
                        @click="sendMessageAgain(index - 1)"
                        >重新回答</el-button
                      >
                      <el-button
                        type="primary"
                        link
                        :icon="CopyDocument"
                        @click="handleCopy(chatMessage.message)"
                        >复制</el-button
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="chat-input">
            <!--            <el-input-->
            <!--              style="height: 100%"-->
            <!--              placeholder="请输入问题, Ctrl+Enter 换行,Enter发送"-->
            <!--            >-->
            <!--              <template #suffix>-->
            <!--                <div class="send-btn">-->
            <!--                  <img src="./assets/send.png" width="20" height="20" />-->
            <!--                </div>-->
            <!--              </template>-->
            <!--            </el-input>-->
            <div class="chat-input-editor">
              <div
                ref="inputMessageEl"
                class="chat-input-words"
                contenteditable="true"
                @input="handleInputWordsChange"
                @keydown="handleKeydown"
              ></div>
              <div class="message-placeholder" v-if="!state.inputMesaage">
                请输入问题, Ctrl+Enter 换行,Enter发送
              </div>
              <div
                v-if="state.thinking"
                class="stop-btn"
                title="终止会话"
                @click="stopReplayMessage"
              >
                <svg
                  data-v-9dc0c993=""
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  aria-hidden="true"
                  role="img"
                  class="!text-3xl hover:text-gray-500 !cursor-pointer iconify iconify--ri"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16M9 9h6v6H9z"
                  ></path>
                </svg>
              </div>
              <div
                v-else
                class="send-btn"
                :class="{ disabled: disableSendMessage }"
                @click="sendMessage(state.inputMesaage)"
              >
                <img src="./assets/send.png" width="20" height="20" />
              </div>
            </div>
          </div>
        </div>
        <!--        <div v-if="state.moreAssistantsExpand" class="right">-->
        <!--          <div-->
        <!--            class="robot-item"-->
        <!--            v-for="(item, index) in state.aiAssistants"-->
        <!--            :class="{ selected: item == state.selectAiAssistant }"-->
        <!--            :key="index"-->
        <!--            @click="handleSelectAiAssistant(item, index)"-->
        <!--          >-->
        <!--            <div class="robot-img-wrap">-->
        <!--              <img-->
        <!--                v-show="item == state.selectAiAssistant"-->
        <!--                class="robot-img"-->
        <!--                src="./assets/op-robot.png"-->
        <!--              />-->
        <!--              <img-->
        <!--                v-show="item != state.selectAiAssistant"-->
        <!--                class="robot-img"-->
        <!--                src="./assets/op-robot-gray.png"-->
        <!--              />-->
        <!--            </div>-->
        <!--            <div class="assistant-info">-->
        <!--              <span class="assistant-name">{{ item.name }}</span>-->
        <!--              <span class="assistant-mark">{{ item.mark }}</span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  nextTick,
  onMounted,
  reactive,
  ref
} from "vue";
import {
  chatCompletions,
  getCurrentUserAiAssistants,
  getHistoryConversations,
  getHistorytChatMessages,
  getHotQuestionList
} from "@/views/modules/ai/api";
import { ElMessage } from "element-plus";
import {
  CloseBold,
  CirclePlusFilled,
  MoreFilled,
  Rank,
  CopyDocument,
  Refresh
} from "@element-plus/icons-vue";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import mdKatex from "@traptitech/markdown-it-katex";
import mila from "markdown-it-link-attributes";
import { v4 } from "uuid";
import { copyHtmlToStyleText } from "@/utils/copy";
const { $confirm } = getCurrentInstance().appContext.config.globalProperties;
interface Assistant {
  code: string;
  name: string;
  mark: string;
  avatar: string;
  accessKey: string;
  appId: string;
}
const aiDialogEl = ref();
const aiHandleEl = ref();
const inputMessageEl = ref();
const chatMessagesWrapEl = ref();
function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">复制</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`;
}

const mdi = new MarkdownIt({
  html: false,
  linkify: true,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language));
    if (validLang) {
      const lang = language ?? "";
      return highlightBlock(
        hljs.highlight(code, { language: lang }).value,
        lang
      );
    }
    return highlightBlock(hljs.highlightAuto(code).value, "");
  }
});

mdi.use(mila, { attrs: { target: "_blank", rel: "noopener" } });
mdi.use(mdKatex, {
  blockClass: "katexmath-block rounded-md p-[10px]",
  errorColor: " #cc0000"
});

const state = reactive({
  handleTranslate: {
    x: 0,
    y: 0
  },
  handleAnimate: true,
  dialogVisible: false,
  isFullScreen: false,
  // ai模型列表
  aiAssistants: <Assistant[]>[],
  moreAssistantsExpand: false,
  selectAiAssistant: <Assistant>{},
  selectAssistantIndex: 0,
  // 历史记录
  historicalrecords: [],
  // 常用问题
  frequentlyAskedQuestions: [],
  // 推荐问题
  suggestList: [],

  // 当前会话ID
  conversationId: v4(),
  // 中间交互信息
  chatMessages: [],
  // 当前输入消息
  inputMesaage: "",
  // 当前回复消息
  replyMessage: "",
  // 思考中
  thinking: false,
  // 请求取消句柄
  handleCancel: null
});

const eventClientPosition = {
  x: 0,
  y: 0,
  clientX: 0,
  clientY: 0
};
const handleDragStart = event => {
  let { clientX, clientY } = event;
  eventClientPosition.clientX = clientX;
  eventClientPosition.clientY = clientY;
  eventClientPosition.x = state.handleTranslate.x;
  eventClientPosition.y = state.handleTranslate.y;
};

const updateHandleTranslate = event => {
  let { clientX, clientY } = event;
  let dx = clientX - eventClientPosition.clientX;
  let dy = clientY - eventClientPosition.clientY;
  state.handleTranslate.x = eventClientPosition.x + dx;
  state.handleTranslate.y = eventClientPosition.y + dy;
};

const handleDrag = event => {
  updateHandleTranslate(event);
};
const handleDragEnd = event => {
  updateHandleTranslate(event);
};

const handleStyle = computed((): CSSProperties => {
  return {
    transform: `translate(${state.handleTranslate.x}px, ${state.handleTranslate.y}px)`
  };
});

// 当前显示的消息
const displayChatMessages = computed(() => {
  let chatMessages = state.chatMessages;
  if (state.thinking) {
    return [
      ...chatMessages,
      {
        role: "assistant",
        message: state.replyMessage || "思考中..."
      }
    ];
  }
  return chatMessages;
});

const openNewSession = () => {
  loadHistoricalrecords();
  clearInputMessage();
  clearSessionMessages();
  state.thinking = false;
  state.replyMessage = "";
  state.conversationId = v4();
};

const openOrQuit = () => {
  state.dialogVisible = !state.dialogVisible;
};

const quit = () => {
  state.dialogVisible = false;
  state.isFullScreen = false;
};

// const showMoreAssistants = () => {
//   state.moreAssistantsExpand = !state.moreAssistantsExpand;
// };

const handleSelectAiAssistant = (item, index) => {
  if (index != state.selectAssistantIndex) {
    const then = () => {
      state.selectAiAssistant = item;
      state.selectAssistantIndex = index;
      openNewSession();
      queryHotQuestionTop10();
      queryQuestionTop3();
    };
    if (state.chatMessages.length == 0) {
      then();
    } else {
      $confirm("切换智能体会打开新的会话，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
        appendTo: aiDialogEl.value
      }).then(then);
    }
  }
};

const handleInputWordsChange = e => {
  state.inputMesaage = e.target.innerText.trim();
};

const handleKeydown = event => {
  // Ctrl + Enter 换行
  if (event.ctrlKey && event.key === "Enter") {
    event.preventDefault(); // 阻止默认换行行为
    const sel = window.getSelection();
    const range =
      sel.rangeCount > 0 ? sel.getRangeAt(0) : document.createRange();
    const br = document.createElement("br"); // 创建换行符
    range.insertNode(br);
    range.setStartAfter(br);
    sel.removeAllRanges();
    sel.addRange(range);
  } else if (event.key === "Enter" && !event.ctrlKey) {
    event.preventDefault(); // 阻止默认换行行为
    if (state.inputMesaage) {
      sendMessage(state.inputMesaage);
    }
  }
};

const accessKey = computed(() => {
  return state.selectAiAssistant?.accessKey;
});

const disableSendMessage = computed(() => {
  return state.thinking || !state.inputMesaage;
});

const clearInputMessage = () => {
  // 清除输入框和内容
  state.inputMesaage = "";
  inputMessageEl.value.innerHTML = "<br/>";
};

const clearSessionMessages = () => {
  // 将消息添加到列表
  state.chatMessages.splice(0, state.chatMessages.length);
};

const beginReplayMessage = msg => {
  // 判断上一次回复的内容
  console.log("beginReplayMessage state.replyMessage", state.replyMessage);
  if (state.replyMessage) {
    finishReplayMessage();
  }

  // 构建历史消息
  let messageRecord = {
    message: msg,
    role: "user"
  };
  // 将消息添加到列表
  state.chatMessages.push(messageRecord);
  clearInputMessage();
  // 设置思考状态
  state.thinking = true;
};

const finishReplayMessage = () => {
  // 防止空消息
  if (state.replyMessage) {
    // 构建历史消息
    let replyRecord = {
      message: state.replyMessage,
      role: "assistant"
    };
    state.chatMessages.push(replyRecord);
    state.replyMessage = "";
  }
  state.thinking = false;
};

const scrollToBottom = () => {
  let wrapEl = chatMessagesWrapEl.value;
  if (wrapEl) {
    wrapEl.scrollTop = wrapEl.scrollHeight;
  }
};

const scrollToTop = () => {
  let wrapEl = chatMessagesWrapEl.value;
  wrapEl.scrollTop = 0;
};

const markdownToHtml = text => {
  try {
    return mdi.render(text);
  } catch (e) {
    return text;
  }
};

const stopReplayMessage = async (onlyStop?) => {
  let cancel = state.handleCancel;
  if (cancel) {
    // 取消是异步操作
    cancel("用户手动终止");
  }
  state.thinking = false;
  if (!onlyStop) {
    nextTick(() => {
      scrollToBottom();
    });
  }
};

/**
 * index位置为问题位置
 *
 * @param index
 */
const sendMessageAgain = index => {
  let chatMessage = displayChatMessages.value[index];
  while (chatMessage && chatMessage.role != "user") {
    chatMessage = displayChatMessages.value[--index];
  }
  if (chatMessage) {
    sendMessage(chatMessage.message);
  }
};

/**
 * 发送消息
 */
const sendMessage = async msg => {
  if (!accessKey.value) {
    ElMessage.error("请选择助手");
    return;
  }
  // 一般在未思考模式下，如果正在思考先停止
  if (state.thinking || state.handleCancel) {
    stopReplayMessage(true);
  }
  // 回复准备
  beginReplayMessage(msg);
  // 构建axios取消句柄
  const cancelFunc = function executor(c) {
    // 保存取消函数
    state.handleCancel = c;
  };
  // 如果有取消的需要确保取消成功
  if (state.handleCancel) {
    // 存在请求未取消,取消调用后onDownloadProgress依然会继续执行, 如果放开会出现两个请求短暂同时消费后台的数据，导致回答内容乱串
    // 使用轮询判断上一个请求是否完成(取消或者正常完成)，这里暂时没有好的处理方案
    await new Promise(resolve => {
      let interval = setInterval(() => {
        if (!state.handleCancel) {
          resolve([]);
          clearInterval(interval);
        }
      }, 10);
    });
  }
  // 强制开始
  state.thinking = true;
  chatCompletions(
    state.conversationId,
    msg,
    accessKey.value,
    ({ event }) => {
      // 处理异步取消延迟带来的问题
      const list = event.target.responseText.split("\n\n");
      let text = "";
      let isRun = true;
      console.log("list", list);
      list.forEach((i: any) => {
        if (i.startsWith("data:Error")) {
          isRun = false;
          text += i.substring(5, i.length);
          // chatStore.updateMessage(aiChatId.value, text, true);
          return;
        }
        if (!i.startsWith("data:{")) {
          return;
        }
        const dataStr = i.substring(5, i.length);
        try {
          const { done, message } = JSON.parse(dataStr);
          if (done || message === null) {
            isRun = false;
            return;
          }
          text += message;
          state.replyMessage = markdownToHtml(text);
          scrollToBottom();
        } catch (e) {
          console.trace(e);
        }
      });
      // end
      state.replyMessage = markdownToHtml(text);
      if (!isRun) {
        finishReplayMessage();
        scrollToBottom();
        return;
      }
    },
    cancelFunc
  )
    .catch(err => {
      if (err?.name === "CanceledError") {
        console.log("请求已取消");
      } else {
        state.replyMessage = "服务器繁忙，请稍后再试。";
      }
      finishReplayMessage();
      return;
    })
    .finally(() => {
      // 请求完成或者取消完成
      state.handleCancel = null;
    });

  nextTick(() => {
    scrollToBottom();
  });
};

const toHistoricalrecord = historyRecord => {
  // stop think
  stopReplayMessage(true);
  // loadHistoricalrecords();
  let { appId, id } = historyRecord;
  // 会话id
  state.conversationId = id;
  let index = -1;
  for (let aiAssistant of state.aiAssistants) {
    ++index;
    if (aiAssistant.appId == appId) {
      state.selectAiAssistant = aiAssistant;
      state.selectAssistantIndex = index;
      break;
    }
  }
  // 查询消息列表
  getHistorytChatMessages(id).then(res => {
    let mdMessages = res.result || [];
    let htmlMessages = mdMessages.map(mdMessage => {
      let { message, ...props } = mdMessage;
      return {
        ...props,
        message: markdownToHtml(message)
      };
    });
    // 先记录清除，然后push
    state.chatMessages.splice(0, state.chatMessages.length, ...htmlMessages);
    nextTick(() => {
      scrollToTop();
    });
  });
};

const loadHistoricalrecords = () => {
  // 查询历史
  getHistoryConversations({
    limit: 10
    // appId: "string",
    // userName: "string",
    // lastDays: 30
  }).then(res => {
    state.historicalrecords = res.data || res.result;
  });
};

const queryHotQuestionTop10 = () => {
  getHotQuestionList({
    appId: state.selectAiAssistant.appId,
    limit: 10,
    random: false
  }).then(res => {
    state.frequentlyAskedQuestions = res.data || res.result;
  });
};

const queryQuestionTop3 = () => {
  getHotQuestionList({
    appId: state.selectAiAssistant.appId,
    limit: 3,
    random: true
  }).then(res => {
    state.suggestList = res.data || res.result;
  });
};

const handleCopy = message => {
  let error = copyHtmlToStyleText(message);
  if (!error) {
    ElMessage.success("文本已复制到剪贴板！");
  } else {
    ElMessage.error("复制失败: " + error);
  }
};

onMounted(() => {
  // 查询智能体列表
  getCurrentUserAiAssistants().then(res => {
    state.aiAssistants = res.data as Assistant[];
    state.selectAiAssistant = (state.aiAssistants &&
      state.aiAssistants[0]) as Assistant;
    state.selectAssistantIndex = 0;

    // 选择助手后再查询热点问题t10 top3
    queryHotQuestionTop10();
    queryQuestionTop3();
  });

  // 加载历史记录
  loadHistoricalrecords();

  aiHandleEl.value.parentNode.addEventListener("dragover", e => {
    e.stopPropagation();
    e.preventDefault();
  });
});
</script>
<style lang="scss">
@import "./styles";

/*暗黑适配*/
html.dark {
  .ai-dialog {
    background: #222020 !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0px 1px 2px 0px rgba(255, 255, 255, 0.4);
    color: rgba(255, 255, 255, 0.9) !important;
    .ai-title {
      position: relative;
      .text {
        font-weight: bold;
        font-size: 24px;
        line-height: 24px;
        text-align: left;
        position: absolute;
        left: 54px;
        white-space: nowrap;
        display: block !important;
      }
    }
    .chat-input {
      background-color: #45454e !important;
      border-color: #595965;
    }
    .ai-main {
      .left {
        background-color: rgba(13, 17, 23, 0.3) !important;
        .list-title {
          span {
            color: rgba(255, 255, 255, 0.9) !important;
          }
        }
        .record-list-item {
          color: rgba(255, 255, 255, 0.6) !important;
          &.selected {
            color: var(--el-color-primary) !important;
          }
        }
      }
    }
    .robot-item {
      background-color: #45454e;
      color: rgba(255, 255, 255, 0.8);
      .assistant-mark {
        color: rgba(255, 255, 255, 0.4) !important;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.ai-robot {
  position: fixed;
  right: 10px;
  bottom: 30%;
  cursor: pointer;
  width: 40px;
  z-index: 10000;
  &-animate {
    animation: jump 1s ease-in-out infinite;
    &:hover {
      animation-play-state: paused;
    }
  }
  &-animateless {
    animation-play-state: paused;
  }
}

.robots-popper {
  .robot-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: 16px;
  }
  .robot-item {
    background-color: #fff;
    box-shadow: 0px 4px 6px 0px rgba(4, 46, 89, 0.1);
    border-radius: 8px;
    width: 250px;
    height: 88px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-left: 20px;
    cursor: pointer;
    .robot-img-wrap {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      .robot-img {
        max-width: unset !important;
      }
    }
    &.selected {
      //background-color: #ecf5ff;
      background-color: var(--el-color-primary) !important;
      .assistant-name,
      .assistant-mark {
        //color: var(--el-color-primary) !important;
        color: #fff !important;
      }
    }
    &.disabled {
      pointer-events: none;
    }
    .assistant-info {
      display: flex;
      flex-direction: column;
      gap: 10px;
      .assistant-name {
        font-weight: 550;
        font-size: 16px;
        //color: #04192a;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
      .assistant-mark {
        font-weight: 400;
        font-size: 12px;
        color: #425663;
        line-height: 12px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}

@keyframes jump {
  0%,
  100% {
    transform: translateY(0) rotateX(0deg) scale(1); /* 初始状态 */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: translateY(-5px) rotateX(-15deg) scale(1.02); /* 减小跳动高度、旋转角度和缩放比例 */
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3); /* 跳起时阴影变化更细腻 */
  }
}

.ai-dialog {
  position: fixed;
  right: 50px;
  bottom: 20px;
  width: 1174px;
  height: 80vh;
  z-index: 3000;
  background: linear-gradient(101deg, #d6eaff 0%, #e8ecef 100%);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04);
  border-radius: 16px 16px 16px 16px;
  transition: all 0.5s;
  transform: translateX(120%);
  visibility: hidden;

  .ai-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 8px;
    .ai-title {
      padding-left: 20px;
      padding-right: 30px;
      display: flex;
      justify-content: space-between;
      .title-img {
        width: 200px;
        height: 25px;
      }
      .text {
        display: none;
      }
    }
    .ai-toolbar {
      display: flex;
      align-items: center;
      margin-right: 20px;
      gap: 20px;
      justify-content: flex-end;
      width: auto;
      height: 25px;
      .assistant-name {
        font-weight: bold;
        color: var(--el-color-primary);
      }
      .img-item {
        cursor: pointer;
        width: 16px;
        height: 16px;
      }
    }
  }
  /*.assistant-more {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
    margin-bottom: 10px;
    gap: 15px;
    .more-text {
      font-weight: 550;
      font-size: 16px;
      color: #04192a;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .assistant-more-img {
      cursor: pointer;
      width: 16px;
      height: 16px;
    }
  }*/
  &.ai-dialog-fullscreen {
    width: 100% !important;
    max-width: unset !important;
    height: 100%;
    bottom: 0;
    right: 0;
  }
  &.ai-dialog-visible {
    transform: translateX(0%);
    visibility: visible;
  }
  .ai-main {
    padding: 20px;
    height: calc(100% - 55px);
    display: flex;
    gap: 20px;
    position: relative;
    .left {
      background-color: #fff;
      width: 282px;
      padding: 20px;
      border-radius: 16px 0px 0px 16px;
      .add-session {
        height: 32px;
        border: 1px dashed var(--el-color-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        cursor: pointer;
        &.disabled {
          pointer-events: none;
          opacity: 0.6;
        }
        .icon {
          color: var(--el-color-primary);
        }
        .text {
          font-family: MiSans, MiSans;
          font-weight: 500;
          font-size: 14px;
          color: var(--el-color-primary);
          line-height: 14px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      .list-title {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 20px;
        span {
          font-weight: 550;
          font-size: 16px;
          color: #04192a;
          line-height: 16px;
          text-align: left;
          font-style: normal;
        }
      }
      .record-list {
        .empty-list {
          opacity: 0.6;
          font-size: 0.9em;
          margin-left: 4px;
        }
        &::-webkit-scrollbar {
          display: none;
        }
        &::-webkit-scrollbar-track-piece {
          display: none;
        }
        .record-list-item {
          font-weight: 400;
          font-size: 14px;
          color: rgba(66, 86, 99, 0.75);
          line-height: 16px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-left: 10px;
          margin-bottom: 12px;
          cursor: pointer;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          &.selected {
            color: var(--el-color-primary) !important;
            font-weight: bold;
          }
          &:hover {
            color: #425663;
            font-weight: 540;
            text-decoration: underline;
          }
        }
      }
    }
    .center {
      width: calc(100% - 282px);
      position: relative;
      .chat-messages {
        height: calc(100% - 50px);
        overflow: auto;
        padding-bottom: 30px;
        &::-webkit-scrollbar {
          width: 4px;
        }
        &::-webkit-scrollbar-track-piece {
          color: #0a79a1;
          width: 4px;
        }
        .chat-tip {
          padding: 20px;
          background-color: var(--color-canvas-default);
          border-radius: 8px;
          .tip {
            font-weight: 550;
            font-size: 16px;
            color: var(--color-fg-default);
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .sub-tip {
            margin: 10px 0 20px 0;
            color: var(--color-fg-subtle);
            font-weight: 400;
            font-size: 14px;
            line-height: 14px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          .suggest {
            display: flex;
            justify-content: space-between;
            .label {
              color: var(--color-fg-default);
              font-weight: 550;
              font-size: 14px;
              line-height: 14px;
            }
          }
          .suggest-lists {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            gap: 4px;
            .suggest-list {
              padding: 12px 16px;
              border-radius: 4px;
              background-color: var(--color-canvas-subtle);
              .suggest-row {
                cursor: pointer;
                display: flex;
                gap: 6px;
                align-items: flex-start;
                img {
                  margin-top: 2px;
                }
                span {
                  flex: 1;
                }
              }
            }
          }
        }
        .chat-message {
          margin-bottom: 28px;
          .message {
            display: flex;
            &.message-user {
              flex-direction: row-reverse;
              .content {
                background-color: var(--el-color-primary) !important;
                color: #fff !important;
              }
              .content-wrap {
                padding-left: 40px;
              }
            }
            &.message-assistant {
              .content-wrap {
                padding-right: 40px;
              }
            }
            .content-wrap {
              max-width: calc(100% - 40px);
              display: flex;
              .content {
                :deep(table) {
                  /** 解决table超出边界问题 */
                  width: unset !important;
                }
                :deep(> div) {
                  max-width: 100%;
                }
                background-color: var(--color-canvas-default);
                border-radius: 8px;
                position: relative;
                max-width: 100%;
                display: flex;
                min-width: 0;
                align-items: flex-end;
                flex-direction: column;
                padding: 10px 16px 20px 16px;
                .message-btn {
                  position: absolute;
                  bottom: 8px;
                  display: none;
                }
                &:hover {
                  .message-btn {
                    display: block;
                  }
                }
              }
            }
          }
        }
      }
      .chat-input {
        width: 100%;
        background-color: #fff;
        border-radius: 10px;
        min-height: 48px;
        padding: 12px 10px;
        position: absolute;
        bottom: 0;
        .chat-input-editor {
          .message-placeholder {
            margin-left: 8px;
            margin-top: 8px;
            position: absolute;
            overflow: hidden;
            text-overflow: ellipsis;
            top: 1px;
            left: 1px;
            user-select: none;
            pointer-events: none;
            color: var(--placeholder-color);
            opacity: 0.333;
            .chat-input-words {
              user-select: text;
              white-space: pre-wrap;
              word-break: break-word;
              width: 100%;
              max-height: fit-content;
              box-sizing: border-box;
              overflow: hidden;
              outline: none;
            }
            margin-bottom: 32px;
          }
          .stop-btn {
            position: absolute;
            bottom: 10px;
            right: 5px;
            width: 40px;
            height: 30px;
          }
          .send-btn {
            position: absolute;
            bottom: 10px;
            right: 5px;
            width: 40px;
            height: 30px;
            background: var(--el-color-primary);
            box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04);
            border-radius: 8px 8px 8px 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            &.disabled {
              pointer-events: none;
              background: gray;
            }
          }
        }
      }
    }
    .right {
      position: absolute;
      right: 20px;
      width: 274px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-items: flex-end;
    }
  }
}
</style>
