import {http} from "@/utils/http";
import {ServerNames} from "@/utils/http/serverNames";
import axios from "axios";

const portalServerPath = `${ServerNames.portalServer}`;

/**
 * 获取当前用户可用的AI信息
 */
export const getCurrentUserAiAssistants = () => {
  return http.get<any, any>(
    `${portalServerPath}/ai/query/getCurrentUserAiAssistants`
  );
};

// /**
//  * 获取当前用户可用的AI信息
//  */
// export const getAiInfoByCode = code => {
//   return http.get<any, any>(
//     `${portalServerPath}/ai/query/getAiInfoByCode?code=${code || ""}`
//   );
// };

/**
 * 处理聊天补全请求
 *
 * @param conversationId 会话ID
 * @param content 发送的消息
 * @param key 机器人标识
 * @param onDownloadProgress
 */
export const chatCompletions = (
  conversationId,
  content,
  key,
  onDownloadProgress,
  cancelFunc: Function
) => {
  return http.post<any, any>(`${ServerNames.aiServer}/v1/chat/completions`, {
    data: {
      conversationId,
      messages: [
        {
          role: "user",
          content
        }
      ]
    },
    headers: {
      "Assistant-Key": key
    },
    onDownloadProgress,
    cancelToken: new axios.CancelToken(cancelFunc as any)
  });
};

/**
 * 获取历史对话
 */
export const getHistoryConversations = params => {
  return http.postJson<any>(
    `${ServerNames.aiServer}/v1/history/getConversations`,
    params
  );
};

/**
 * 根据会话id查询消息列表
 */
export const getHistorytChatMessages = conversationId => {
  return http.postJson<any>(
    `${ServerNames.aiServer}/v1/history/getChatMessages?conversationId=${conversationId || ""}`,
    {}
  );
};

/**
 * 查询热点问题
 *
 * @param query
 */
export const getHotQuestionList = query => {
  return http.get<any, any>(
    `${ServerNames.aiServer}/v1/question/getHotQuestionList`,
    {
      params: query
    }
  );
};

/**
 * ai通用文本解析
 *
 * @param key
 * @param text
 * @param onDownloadProgress
 * @param cancelFunc
 */
export const ragChar = (
  key: string,
  params: any,
  onDownloadProgress,
  cancelFunc: Function
) => {
  return http.post<any, any>(`${ServerNames.aiServer}/v1/rag/chat`, {
    data: {
      inputParams: params
    },
    headers: {
      "Assistant-Key": key || "langchat-3a686684436e4f0497290929b72c738b"
    },
    onDownloadProgress,
    cancelToken: new axios.CancelToken(cancelFunc as any)
  });
};

// /ai/query/getAiInfoByCode

/**
 * 根据code获取AI信息
 */
export const getAiInfoByCode = code => {
  return http.get<any, any>(
    `${portalServerPath}/ai/query/getAiInfoByCode?code=${code || ""}`
  );
};
