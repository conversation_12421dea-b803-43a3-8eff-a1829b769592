
.markdown-body {
	background-color: transparent;
	font-size: 14px;

	p {
		white-space: pre-wrap;
	}

	ol {
		list-style-type: decimal;
	}

	ul {
		list-style-type: disc;
	}

	pre code,
	pre tt {
		line-height: 1.65;
	}

	.highlight pre,
	pre {
		background-color: #fff;
	}

	code.hljs {
		padding: 0;
	}

	.code-block {
		&-wrapper {
			position: relative;
			padding-top: 24px;
      margin-top: 10px;
		}

		&-header {
			position: absolute;
			top: 5px;
			right: 0;
			width: 100%;
			padding: 0 1rem;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			color: #b3b3b3;

			&__copy {
				cursor: pointer;
				margin-left: 0.5rem;
				user-select: none;

				&:hover {
					color: #65a665;
				}
			}
		}
	}

}

html.dark {

	.message-reply {
		.whitespace-pre-wrap {
			white-space: pre-wrap;
			color: var(--n-text-color);
		}
	}

	.highlight pre,
	pre {
		background-color: #282c34;
	}
}

@keyframes blink {
	0%, to {
		background-color: currentColor
	}
	50% {
		background-color: transparent
	}
}

.animate-blink {
	animation: blink 1.2s infinite steps(1, start)
}
