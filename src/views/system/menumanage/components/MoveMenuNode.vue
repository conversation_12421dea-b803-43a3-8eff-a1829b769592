<template>
  <el-dialog
    :title="'移动菜单 - ' + currentNode.resourceName"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    destroy-on-close
    width="35%"
  >
    <div class="space-x-2">
      <span>当前上级菜单：</span>
      <span class="font-bold">{{ parentMenuName }}</span>
    </div>
    <div class="flex space-x-2 mt-2">
      <span class="w-[123px]">目标上级菜单：</span>
      <div
        class="border border-gray-400 border-opacity-20 rounded-lg h-content w-full h-60 overflow-y-auto ml-1 p-1 dark:bg-dark-color"
      >
        <el-tree
          ref="menuTreeRef"
          :data="menuData"
          node-key="resourceId"
          :expand-on-click-node="false"
          highlight-current
          :props="{ label: 'resourceName', isLeaf: 'hasChildren' }"
          :default-expanded-keys="['-1']"
          lazy
          :load="loadNode"
          @current-change="treeSelectHandler"
        />
      </div>
    </div>
    <template #footer>
      <el-button
        type="primary"
        :icon="useRenderIcon('EP-Promotion')"
        :disabled="
          currentNode.parentId == selectedNode?.resourceId ||
          currentNode.resourceId == selectedNode?.resourceId ||
          selectedNode == null
        "
        @click="moveMenuNode"
      >
        移 动
      </el-button>
      <el-button @click="cancel" :icon="useRenderIcon('EP-CircleClose')">
        取 消
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { getCurrentInstance, reactive, ref, toRefs, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import type Node from "element-plus/es/components/tree/src/model/node";
import { ResultStatus } from "@/utils/http/types";
import {
  getMenuById,
  getMenuData,
  ManageResource,
  updateMenu
} from "@/views/system/menumanage/api/MenuManageApi";

const { $confirm, $notify } =
  getCurrentInstance().appContext.config.globalProperties;
const menuTreeRef = ref<InstanceType<typeof ElTree>>();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  node: {
    type: Object
  }
});

//定义事件
const emit = defineEmits(["update:visible", "move-success"]);

//数据对象
const state = reactive({
  dialogVisible: false,
  currentNode: {} as ManageResource,
  selectedNode: null,
  menuData: [] as Array<ManageResource>,
  parentMenuName: ""
});
const { dialogVisible, currentNode, selectedNode, menuData, parentMenuName } =
  toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }

  state.currentNode = newValue.node;
  state.selectedNode = null;
  loadRootMenuData();
  loadParentMenuName(state.currentNode.parentId);
});

const rootNode = {
  resourceId: "-1",
  resourceName: "顶级菜单",
  children: [],
  hasChildren: false
};

//加载首层菜单树数据
const loadRootMenuData = () => {
  state.menuData = [];
  state.menuData = [rootNode] as Array<ManageResource>;
};

//加载原父菜单名称
const loadParentMenuName = (parentId: string) => {
  if (parentId === "-1") {
    state.parentMenuName = "顶级菜单";
  } else {
    getMenuById(parentId).then(res => {
      if (res.data != null) {
        state.parentMenuName = res.data.resourceName;
      }
    });
  }
};

//反转hasChildren属性 以作为isLeaf的标记
const reverseHasChildren = (data: Array<ManageResource>) => {
  data.forEach(d => (d.hasChildren = !d.hasChildren));
};

//加载树节点
const loadNode = (node: Node, resolve: (data: ManageResource[]) => void) => {
  if (node.data.resourceId != null) {
    getMenuData({ parentId: node.data.resourceId }).then(result => {
      reverseHasChildren(result.data);
      resolve(result.data);
    });
  } else {
    state.menuData = [rootNode] as Array<ManageResource>;
  }
};

//选择树节点触发
const treeSelectHandler = (node: ManageResource) => {
  state.selectedNode = node;
};

//移动菜单树节点
const moveMenuNode = () => {
  $confirm(
    `您确认要将 '${state.currentNode.resourceName}' 移动到 '${state.selectedNode.resourceName}' 下么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  ).then(() => {
    state.currentNode.parentId = state.selectedNode.resourceId;
    const successMsg = `已成功将菜单移动到 ${state.selectedNode.resourceName}！`;
    updateMenu(state.currentNode).then(res => {
      if (ResultStatus.Success === res.status) {
        $notify({
          title: "提示",
          message: successMsg,
          type: "success"
        });
        emit("move-success");
        cancel();
      }
    });
  });
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
