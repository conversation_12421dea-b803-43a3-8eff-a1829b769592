<template>
  <div>
    <splitpane :splitSet="settingLR">
      <template #paneL>
        <div class="pb-1.5 ml-1">
          <el-input
            placeholder="字典类型名称"
            clearable
            :suffix-icon="useRenderIcon('EP-Search')"
            v-model="typeSearchKeyWord"
          >
            <template #prepend>
              <el-button
                type="primary"
                :icon="useRenderIcon('RI-AddLine')"
                @click="openAddTypeDialog"
                v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
              >
                添加
              </el-button>
            </template>
          </el-input>
        </div>
        <div
          class="border border-gray-400 border-opacity-20 rounded-lg h-content overflow-y-auto ml-1 dark:bg-dark-color"
        >
          <el-tree
            ref="typeTreeRef"
            :data="typeData"
            node-key="dictTypeSign"
            :expand-on-click-node="false"
            highlight-current
            :props="tenantTreeProps"
            :filter-node-method="filterTypeNode"
            @current-change="typeSelectChange"
          >
            <template #default="{ node, data }">
              <div class="tree-list-content">
                <div class="tree-node-left">
                  <IconifyIconOffline icon="RI-Book2Fill"/>
                  <span>{{ node.label }}</span>
                </div>

                <div
                  class="space-x-2"
                  v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
                >
                  <el-link
                    type="primary"
                    :icon="useRenderIcon('RI-EditBoxFill')"
                    @click.stop="openEditTypeDialog(data)"
                  />
                  <el-link
                    type="danger"
                    :disabled="data.protectLevel < 2"
                    :icon="useRenderIcon('RI-DeleteBin6Fill')"
                    @click.stop="deleteTypeHandler(data)"
                  />
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </template>
      <template #paneR>
        <div class="ml-1.5 mr-1">
          <avue-crud
            :option="dictItemCrudOption"
            v-model:search="dictSearchForm"
            v-model:page="page"
            :data="dictData"
            :table-loading="loading"
            @row-save="addDictItem"
            @row-update="updateDictItem"
            @refresh-change="doSearchItemData"
            @current-change="doSearchItemData"
            @size-change="doSearchItemData"
            @row-del="deleteDictItem"
          >
            <template #menu-right="{ size }">
              <div class="float-left pr-3">
                <el-input
                  clearable
                  placeholder="字典标识/名称"
                  v-model="dictSearchForm.keyWord"
                  :size="size"
                  @keyup.enter.prevent="searchFirstPageData(null, null)"
                  @blur="dictSearchForm.keyWord = ($event.target as HTMLInputElement).value.trim()"
                >
                  <template #append>
                    <el-button
                      :icon="useRenderIcon('EP-Search')"
                      @click.stop="searchFirstPageData(null, null)"
                    />
                  </template>
                </el-input>
              </div>
              <el-tooltip
                content="关联数据管理"
                placement="top"
                :open-delay="1000"
                v-if="
                  selectedDictType != null && selectedDictType.protectLevel > 0
                "
              >
                <el-button
                  :icon="useRenderIcon('RI-SendToBack')"
                  circle
                  :size="size"
                  :disabled="linkedManageDisabled"
                  @click="openLinkedDictManageDialog"
                  v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
                />
              </el-tooltip>
              <el-tooltip
                content="导出字典数据"
                placement="top"
                :open-delay="1000"
              >
                <el-button
                  :icon="useRenderIcon('EP-Download')"
                  circle
                  :size="size"
                  :disabled="dictData.length < 1"
                  @click="exportDictHandler"
                  v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
                />
              </el-tooltip>
              <el-tooltip
                content="导入字典数据"
                placement="top"
                :open-delay="1000"
              >
                <el-button
                  :icon="useRenderIcon('EP-Upload')"
                  circle
                  :size="size"
                  @click="uploadDictVisible = true"
                  v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
                />
              </el-tooltip>
            </template>
          </avue-crud>
        </div>
      </template>
    </splitpane>

    <!-- 字典管理管理对话框 -->
    <dict-link-manage
      v-model:visible="linkManageVisible"
      :parent-dict-type="selectedDictType"
    />

    <!-- 导入字典数据对话框 -->
    <upload-dictionary-data
      v-model:visible="uploadDictVisible"
      :type-sign="selectedDictType.dictTypeSign"
      @import-success="importSuccessHandler"
    />
  </div>
</template>

<script lang="ts" setup>
import splitpane, {ContextProps} from "@/components/ReSplitPane";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import {ElTree} from "element-plus";
import DictionaryTypeEditor from "@/views/system/dictionary/components/DictionaryTypeEditor.vue";
import {addDialog, closeAllDialog} from "@/components/ReDialog/index";
import {ResultStatus} from "@/utils/http/types";
import {validSignId} from "@/utils/validator";
import DictLinkManage from "@/views/system/dictionary/components/DictLinkManage.vue";
import {message} from "@/utils/message";
import UploadDictionaryData from "@/views/system/dictionary/components/UploadDictionaryData.vue";
import {AdminEnum} from "@/utils/CommonTypes";
import {hasAuth} from "@/router/utils";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {computed, getCurrentInstance, h, nextTick, reactive, ref, toRefs, watch} from "vue";
import {
  addDictItemData,
  deleteDictionaryType,
  deleteDictItemData,
  DictionaryItemInfo,
  DictionaryTypeInfo,
  exportDictItemData,
  getDictTypeList,
  searchDictItemData,
  updateDictItemData
} from "@/views/system/dictionary/api/DictManageApi";

defineOptions({
  name: "SystemConfig_dictionary"
});

const {$notify, $confirm} =
  getCurrentInstance().appContext.config.globalProperties;

const typeTreeRef = ref<InstanceType<typeof ElTree>>();
const typeEditorRef = ref<InstanceType<typeof DictionaryTypeEditor>>();

//页面部署配置
const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

//类型列表配置
const tenantTreeProps = {
  label: "dictTypeName"
};

//数据对象
const state = reactive({
  uploadDictVisible: false,
  typeData: [] as Array<DictionaryTypeInfo>,
  dictData: [] as Array<DictionaryItemInfo>,
  selectedDictType: {} as DictionaryTypeInfo,
  dictSearchForm: {
    keyWord: "",
    itemLabel: "",
    itemValue: "",
    dictTypeSign: "",
    pageNo: 1,
    pageSize: defaultPageSize
  },
  page: {
    total: 0,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  linkManageVisible: false,
  typeSearchKeyWord: "",
  loading: false
});
const {
  uploadDictVisible,
  typeData,
  dictData,
  dictSearchForm,
  page,
  linkManageVisible,
  selectedDictType,
  typeSearchKeyWord,
  loading
} = toRefs(state);

watch(typeSearchKeyWord, val => {
  typeTreeRef.value!.filter(val);
});

//字典配置项标识校验
const validateItemSign = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：buss_dict"));
  }
};

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 200;
});
const dictItemCrudOption = reactive({
  searchMenuSpan: 5,
  align: "center",
  menuAlign: "center",
  stripe: true,
  border: true,
  labelWidth: 120,
  editBtn: true,
  delBtn: true,
  viewBtn: true,
  height: tableHeight,
  menu: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  addBtn: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  column: [
    {
      label: "字典标识",
      prop: "itemValue",
      editDisabled: true,
      maxlength: 50,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请填写字典标识",
          trigger: "blur"
        },
        {validator: validateItemSign, trigger: "blur"}
      ]
    },
    {
      label: "字典名称",
      prop: "itemLabel",
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请填写字典类型名称",
          trigger: "blur"
        }
      ]
    },
    {
      label: "所属类型",
      prop: "dictTypeName",
      addDisabled: true,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "上联字典名称",
      prop: "parentItemLabel",
      search: false,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "上联字典标识",
      prop: "parentItemValue",
      search: false,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "排序",
      prop: "sortIndex",
      type: "number",
      min: 0,
      max: 999,
      value: 0
    },
    {
      label: "备注",
      prop: "remarks",
      type: "textarea",
      row: true,
      span: 24,
      minRows: 3
    },
    {
      label: "最近更新UID",
      prop: "updateUid",
      addDisabled: true,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "创建时间",
      prop: "createTime",
      addDisabled: true,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "更新时间",
      prop: "updateTime",
      addDisabled: true,
      addDisplay: false,
      editDisplay: false
    }
  ]
});

//加载字典类型数据
const loadDictTypeData = async () => {
  getDictTypeList().then(res => {
    state.typeData = res.data;

    //默认选中第一个
    if (state.typeData && state.typeData.length > 0) {
      const firstType = state.typeData[0];
      nextTick(() => {
        typeTreeRef.value!.setCurrentKey(firstType.dictTypeSign);
      });
    } else {
      typeTreeRef.value!.setCurrentKey(null);
    }
  });
};
loadDictTypeData();

//字典类型选择改变
const typeSelectChange = (type: DictionaryTypeInfo) => {
  state.selectedDictType = type;
  dealDataProtect(type);
  searchFirstPageData(null, null);
};

//处理数据保护
const dealDataProtect = (type: DictionaryTypeInfo) => {
  if (
    type.protectLevel === 0 &&
    hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin])
  ) {
    dictItemCrudOption.addBtn = false;
    dictItemCrudOption.editBtn = false;
    dictItemCrudOption.delBtn = false;
  } else if (
    type.protectLevel === 1 &&
    hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin])
  ) {
    dictItemCrudOption.addBtn = true;
    dictItemCrudOption.editBtn = true;
    dictItemCrudOption.delBtn = false;
  } else if (hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin])) {
    dictItemCrudOption.addBtn = true;
    dictItemCrudOption.editBtn = true;
    dictItemCrudOption.delBtn = true;
  }
};

//搜索首页数据
const searchFirstPageData = (params, done) => {
  state.page.currentPage = 1;
  state.dictSearchForm.itemLabel = state.dictSearchForm.keyWord;
  state.dictSearchForm.itemValue = state.dictSearchForm.keyWord;
  doSearchItemData(params, done);
};

//搜索字典数据
const doSearchItemData = async (params, done) => {
  if (state.selectedDictType.dictTypeSign != null) {
    state.dictData = [];
    state.dictSearchForm.dictTypeSign = state.selectedDictType.dictTypeSign;
    state.dictSearchForm.pageNo = state.page.currentPage;
    state.dictSearchForm.pageSize = state.page.pageSize;
    state.loading = true;
    await searchDictItemData(state.dictSearchForm).then(result => {
      state.dictData = result.data;
      state.page.total = parseInt(result.total);
    });
    state.loading = false;
    if (done != null) {
      done();
    }
  }
};

//打开新增类型窗口
const openAddTypeDialog = () => {
  openEditTypeDialog({} as DictionaryTypeInfo);
};

//打开编辑类型窗口
const openEditTypeDialog = (type: DictionaryTypeInfo) => {
  addDialog({
    title: type.dictTypeSign ? "编辑类型" : "新增类型",
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: () =>
      h(DictionaryTypeEditor, {
        ref: typeEditorRef,
        onSubmit: (res: boolean) => {
          if (res) {
            closeAllDialog();
            loadDictTypeData();
          }
        }
      }),
    open: () => {
      typeEditorRef.value.loadBussModuleData();
      typeEditorRef.value.loadCanLikedDictTypeData(type.dictTypeSign);
      if (type.dictTypeSign) {
        typeEditorRef.value.loadTypeDetailInfo(type.dictTypeSign);
      } else {
        typeEditorRef.value.resetEditForm({});
      }
    },
    beforeSure: () => {
      typeEditorRef.value.submitForm();
    }
  });
};

//删除类型
const deleteTypeHandler = (type: DictionaryTypeInfo) => {
  $confirm(
    `删除字典类型，其所属的字典数据也会删除。您确定要删除 '${type.dictTypeName}' 么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    deleteDictionaryType(type.dictTypeSign).then(res => {
      if (res.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功删除类型信息！",
          type: "success"
        });
        loadDictTypeData();
      }
    });
  });
};

//新增字典
const addDictItem = async (form, done) => {
  form.dictTypeSign = state.selectedDictType.dictTypeSign;
  form.dictTypeName = state.selectedDictType.dictTypeName;
  await addDictItemData(form).then(res => {
    if (res.status == ResultStatus.Success) {
      $notify({
        title: "提示",
        message: "已成功添加字典数据！",
        type: "success"
      });
      searchFirstPageData(null, null);
    }
  });
  done();
};

//更新字典
const updateDictItem = async (form, index, done) => {
  form.dictTypeSign = state.selectedDictType.dictTypeSign;
  form.dictTypeName = state.selectedDictType.dictTypeName;
  await updateDictItemData(form).then(res => {
    if (res.status == ResultStatus.Success) {
      $notify({
        title: "提示",
        message: "已成功更新字典数据！",
        type: "success"
      });
      doSearchItemData(null, null);
    }
  });
  done();
};

//删除字典数据
const deleteDictItem = async form => {
  $confirm("您确定要删除 '" + form.itemLabel + "' 吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deleteDictItemData(form.id).then(res => {
      if (res.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功删除字典数据！",
          type: "success"
        });
        doSearchItemData(null, null);
      }
    });
  });
};

//打开字典数据关联管理接口
const openLinkedDictManageDialog = () => {
  state.linkManageVisible = true;
};

//判断当前选择类型是否可以进行关联数据管理操作
const linkedManageDisabled = computed(() => {
  return !(
    state.selectedDictType != null &&
    state.selectedDictType.linkedTypeSign != null &&
    state.selectedDictType.linkedTypeSign.length > 0
  );
});

//导出字典项数据触发
const exportDictHandler = () => {
  message("数据正在导出中...", {type: "success"});
  state.dictSearchForm.dictTypeSign = state.selectedDictType.dictTypeSign;
  exportDictItemData(state.dictSearchForm);
};

//成功导入字典数据触发
const importSuccessHandler = () => {
  doSearchItemData(null, null);
};

//字典类型搜索
const filterTypeNode = (value: string, data: DictionaryTypeInfo) => {
  if (!value) return true;
  return data.dictTypeName.includes(value);
};
</script>

<style scoped>
.h-content {
  height: calc(100% - 48px);
}
</style>
