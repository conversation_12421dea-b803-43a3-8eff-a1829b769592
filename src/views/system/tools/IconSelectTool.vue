<template>
  <div>
    <div class="flex-bc">
      <div class="flex-sc space-x-2">
        <span class="text-xs">图标集：</span>
        <el-select
          v-model="queryForm.selSet"
          @change="setChangeHandler"
          style="width: 120px"
        >
          <el-option
            v-for="s in offlineIcon.iconSets"
            :key="s.prefix"
            :value="s.prefix"
            :label="s.setName"
          />
        </el-select>
        <span class="text-xs pl-3">分组：</span>
        <el-select
          v-model="queryForm.selGroup"
          filterable
          @change="groupChangeHandler"
          style="width: 120px"
        >
          <el-option
            v-for="g in groupData"
            :key="g.groupCode"
            :value="g.groupCode"
            :label="g.groupName"
          />
        </el-select>
        <div
          class="flex-sc pl-2 pr-2 bg-gray-200 dark:bg-dark-color h-[22px] rounded"
        >
          <IconifyIconOffline
            icon="EP-WarningFilled"
            width="12"
            color="#FFC107"
          />
          <span class="pl-2 text-xs/[12px]"> 点击即可复制图标名称 </span>
        </div>
      </div>
      <div>
        <el-input
          clearable
          placeholder="图标名称"
          v-model="keyWord"
          :suffix-icon="useRenderIcon('EP-Search')"
          @blur="keyWord = ($event.target as HTMLInputElement).value.trim()"
        />
      </div>
    </div>
    <el-space wrap class="p-5" :size="15">
      <icon-copy-viewer
        v-for="(name, index) in iconData"
        :key="index"
        :name="queryForm.selSet + name"
        :label="queryForm.selSet + name"
      />
    </el-space>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, toRefs } from "vue";
import offlineIcon, {
  epPrefix,
  getSetByPrefix,
  getSetGroup
} from "@/components/ReIcon/src/offlineIcon";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import IconCopyViewer from "@/views/system/tools/components/IconCopyViewer.vue";

//数据对象
const state = reactive({
  keyWord: null as string,
  queryForm: {
    selSet: epPrefix,
    selGroup: "system"
  },
  groupData: getSetByPrefix(epPrefix).groups,
  iconNames: getSetGroup(epPrefix, "system").iconNames
});

const { keyWord, queryForm, groupData } = toRefs(state);

//图标集选择改变处理器
const setChangeHandler = (prefix: string) => {
  state.groupData = getSetByPrefix(prefix).groups;
  state.queryForm.selGroup = null;
  state.iconNames = [];
};

//分组改变处理器
const groupChangeHandler = (code: string) => {
  const group = getSetGroup(state.queryForm.selSet, code);
  state.iconNames = group.iconNames;
};

//图标集计算数据
const iconData = computed(() => {
  if (state.keyWord != null) {
    return state.iconNames.filter(item => {
      if (item.toLowerCase().includes(state.keyWord.toLowerCase())) {
        return item;
      }
    });
  } else {
    return state.iconNames;
  }
});
</script>
